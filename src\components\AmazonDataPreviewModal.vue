<template>
  <div v-if="visible" class="amazon-data-preview-modal" @click="handleBackdropClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>Amazon商品数据预览</h3>
        <button class="close-btn" @click="closeModal">×</button>
      </div>
      
      <div class="modal-body">
        <div class="tabs">
          <button 
            :class="['tab-btn', { active: activeTab === 'raw' }]"
            @click="activeTab = 'raw'"
          >
            原始数据
          </button>
          <button 
            :class="['tab-btn', { active: activeTab === 'formatted' }]"
            @click="activeTab = 'formatted'"
          >
            组装数据
          </button>
          <button 
            :class="['tab-btn', { active: activeTab === 'preview' }]"
            @click="activeTab = 'preview'"
          >
            数据预览
          </button>
        </div>
        
        <div class="tab-content">
          <!-- 原始数据标签页 -->
          <div v-if="activeTab === 'raw'" class="tab-panel">
            <h4>Amazon原始数据</h4>
            <pre class="json-display">{{ formatJson(rawData) }}</pre>
          </div>
          
          <!-- 组装数据标签页 -->
          <div v-if="activeTab === 'formatted'" class="tab-panel">
            <h4>店小秘格式数据</h4>
            <pre class="json-display">{{ formatJson(formattedData) }}</pre>
          </div>
          
          <!-- 数据预览标签页 -->
          <div v-if="activeTab === 'preview'" class="tab-panel">
            <h4>数据预览</h4>
            <div class="preview-grid">
              <div class="preview-item">
                <label>商品名称:</label>
                <span>{{ formattedData?.productName || '未获取' }}</span>
              </div>
              <div class="preview-item">
                <label>ASIN:</label>
                <span>{{ rawData?.asin || '未获取' }}</span>
              </div>
              <div class="preview-item">
                <label>价格:</label>
                <span>${{ rawData?.price || '未获取' }}</span>
              </div>
              <div class="preview-item">
                <label>评分:</label>
                <span>{{ rawData?.rating || '未获取' }} ({{ rawData?.reviewCount || 0 }} 评论)</span>
              </div>
              <div class="preview-item">
                <label>分类ID:</label>
                <span>{{ formattedData?.categoryId || '未设置' }}</span>
              </div>
              <div class="preview-item">
                <label>店铺ID:</label>
                <span>{{ formattedData?.shopId || '未设置' }}</span>
              </div>
              <div class="preview-item">
                <label>图片数量:</label>
                <span>{{ rawData?.imageUrls?.length || 0 }} 张</span>
              </div>
              <div class="preview-item">
                <label>变体数量:</label>
                <span>{{ rawData?.variations?.length || 0 }} 个</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="closeModal">关闭</button>
        <button class="btn btn-primary" @click="copyToClipboard">复制JSON</button>
        <button class="btn btn-success" @click="downloadJson">下载JSON</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { AmazonProductData, DianxiaomiProductData } from '../services/amazonDataService'

interface Props {
  visible: boolean
  rawData: AmazonProductData | null
  formattedData: DianxiaomiProductData | null
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const activeTab = ref<'raw' | 'formatted' | 'preview'>('preview')

const formatJson = (data: any): string => {
  if (!data) return '暂无数据'
  return JSON.stringify(data, null, 2)
}

const closeModal = () => {
  emit('close')
}

const handleBackdropClick = (e: MouseEvent) => {
  if (e.target === e.currentTarget) {
    closeModal()
  }
}

const copyToClipboard = async () => {
  try {
    const dataToCopy = activeTab.value === 'raw' ? props.rawData : props.formattedData
    await navigator.clipboard.writeText(JSON.stringify(dataToCopy, null, 2))
    alert('数据已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    alert('复制失败')
  }
}

const downloadJson = () => {
  try {
    const dataToCopy = activeTab.value === 'raw' ? props.rawData : props.formattedData
    const dataStr = JSON.stringify(dataToCopy, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `amazon-data-${activeTab.value}-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载失败:', error)
    alert('下载失败')
  }
}
</script>

<style scoped>
.amazon-data-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1000px;
  max-height: 90%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tab-btn {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  background: #f5f5f5;
}

.tab-btn.active {
  border-bottom-color: #ff4d4f;
  color: #ff4d4f;
}

.tab-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.tab-panel h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.json-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow: auto;
  max-height: 400px;
  white-space: pre-wrap;
  word-break: break-all;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.preview-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.preview-item label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.preview-item span {
  color: #333;
  font-size: 14px;
  word-break: break-all;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #1e7e34;
}
</style>
