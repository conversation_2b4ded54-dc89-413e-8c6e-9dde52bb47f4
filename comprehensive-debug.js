// 全面的Anti-Content调试和拦截脚本
// 在Temu商家后台页面控制台运行

console.log('🔍 开始全面调试Anti-Content拦截问题...');

// 全局状态
const debugState = {
    interceptedRequests: [],
    antiContentFound: null,
    mallIdFound: null,
    originalFetch: null,
    originalXHR: {
        open: null,
        setRequestHeader: null,
        send: null
    }
};

// 1. 设置全面的网络拦截
function setupComprehensiveInterception() {
    console.log('🌐 设置全面的网络拦截...');
    
    // 保存原始方法
    debugState.originalFetch = window.fetch;
    debugState.originalXHR.open = XMLHttpRequest.prototype.open;
    debugState.originalXHR.setRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
    debugState.originalXHR.send = XMLHttpRequest.prototype.send;
    
    // 拦截fetch
    window.fetch = function(...args) {
        const [url, options] = args;
        const urlString = typeof url === 'string' ? url : url.toString();
        
        console.log('🌐 Fetch请求:', urlString);
        
        if (urlString.includes('seller.kuajingmaihuo.com')) {
            console.log('🎯 拦截到Temu API请求 (fetch):', urlString);
            console.log('📋 请求选项:', options);
            
            if (options && options.headers) {
                console.log('📋 请求头详情:', options.headers);
                
                // 检查Anti-Content
                const headers = options.headers;
                if (headers['Anti-Content']) {
                    debugState.antiContentFound = headers['Anti-Content'];
                    console.log('🎉 找到 Anti-Content (fetch):', headers['Anti-Content']);
                    
                    // 保存到localStorage
                    try {
                        localStorage.setItem('debug_anti_content', headers['Anti-Content']);
                        localStorage.setItem('debug_anti_content_time', Date.now().toString());
                    } catch (e) {}
                }
                
                // 检查mallid
                if (headers['mallid']) {
                    debugState.mallIdFound = headers['mallid'];
                    console.log('🎉 找到 mallid (fetch):', headers['mallid']);
                    
                    // 保存到localStorage
                    try {
                        localStorage.setItem('debug_mall_id', headers['mallid']);
                        localStorage.setItem('debug_mall_id_time', Date.now().toString());
                    } catch (e) {}
                }
            }
            
            // 记录请求
            debugState.interceptedRequests.push({
                type: 'fetch',
                url: urlString,
                options: options,
                timestamp: Date.now()
            });
        }
        
        return debugState.originalFetch.apply(this, args);
    };
    
    // 拦截XMLHttpRequest
    const xhrInstances = new WeakMap();
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        const urlString = url.toString();
        xhrInstances.set(this, { 
            method, 
            url: urlString, 
            headers: {},
            timestamp: Date.now()
        });
        
        console.log('🌐 XHR open:', method, urlString);
        
        return debugState.originalXHR.open.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
        const instance = xhrInstances.get(this);
        if (instance) {
            instance.headers[name] = value;
            
            if (instance.url.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 XHR设置头部 (Temu API):', name, '=', value.substring(0, 100) + (value.length > 100 ? '...' : ''));
                
                if (name.toLowerCase() === 'Anti-Content') {
                    debugState.antiContentFound = value;
                    console.log('🎉 找到 Anti-Content (XHR):', value);
                    
                    // 保存到localStorage
                    try {
                        localStorage.setItem('debug_anti_content', value);
                        localStorage.setItem('debug_anti_content_time', Date.now().toString());
                    } catch (e) {}
                }
                
                if (name.toLowerCase() === 'mallid') {
                    debugState.mallIdFound = value;
                    console.log('🎉 找到 mallid (XHR):', value);
                    
                    // 保存到localStorage
                    try {
                        localStorage.setItem('debug_mall_id', value);
                        localStorage.setItem('debug_mall_id_time', Date.now().toString());
                    } catch (e) {}
                }
            }
        }
        
        return debugState.originalXHR.setRequestHeader.call(this, name, value);
    };
    
    XMLHttpRequest.prototype.send = function(body) {
        const instance = xhrInstances.get(this);
        if (instance && instance.url.includes('seller.kuajingmaihuo.com')) {
            console.log('🎯 XHR发送请求 (Temu API):', instance.method, instance.url);
            console.log('📋 所有头部:', instance.headers);
            
            // 记录请求
            debugState.interceptedRequests.push({
                type: 'xhr',
                method: instance.method,
                url: instance.url,
                headers: instance.headers,
                body: body,
                timestamp: instance.timestamp
            });
        }
        
        return debugState.originalXHR.send.call(this, body);
    };
    
    console.log('✅ 网络拦截设置完成');
}

// 2. 监控页面变化
function setupPageMonitoring() {
    console.log('👀 设置页面变化监控...');
    
    // 监控URL变化
    let lastUrl = window.location.href;
    setInterval(() => {
        if (window.location.href !== lastUrl) {
            lastUrl = window.location.href;
            console.log('🔄 页面URL变化:', lastUrl);
            // URL变化时重新检查
            setTimeout(checkForAntiContent, 2000);
        }
    }, 1000);
    
    // 监控DOM变化
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 检查是否有新的script标签
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const scripts = node.querySelectorAll ? node.querySelectorAll('script') : [];
                        if (scripts.length > 0) {
                            console.log('📜 检测到新的script标签:', scripts.length);
                            setTimeout(checkForAntiContent, 1000);
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('✅ 页面监控设置完成');
}

// 3. 检查Anti-Content
function checkForAntiContent() {
    console.log('🔍 检查页面中的Anti-Content...');
    
    // 检查script标签
    const scripts = document.querySelectorAll('script');
    console.log('📜 找到script标签数量:', scripts.length);
    
    for (let i = 0; i < scripts.length; i++) {
        const script = scripts[i];
        const content = script.textContent || script.innerHTML;
        if (!content) continue;
        
        if (content.toLowerCase().includes('Anti-Content') || content.toLowerCase().includes('anticontent')) {
            console.log(`🎯 第${i+1}个script包含Anti-Content关键词`);
            console.log('📜 内容片段:', content.substring(0, 500));
            
            // 尝试提取Anti-Content
            const patterns = [
                /["']Anti-Content["']\s*:\s*["']([^"']+)["']/gi,
                /Anti-Content["']\s*:\s*["']([^"']+)["']/gi,
                /"Anti-Content":\s*"([^"]+)"/gi,
                /'Anti-Content':\s*'([^']+)'/gi
            ];
            
            for (const pattern of patterns) {
                const matches = content.matchAll(pattern);
                for (const match of matches) {
                    console.log('🎉 在script中找到Anti-Content:', match[1]);
                    debugState.antiContentFound = match[1];
                    
                    // 保存到localStorage
                    try {
                        localStorage.setItem('debug_anti_content', match[1]);
                        localStorage.setItem('debug_anti_content_time', Date.now().toString());
                    } catch (e) {}
                }
            }
        }
    }
    
    // 检查window对象
    const globalVars = ['__ANTI_CONTENT__', '__INITIAL_STATE__', 'g_config', '__APP_STATE__'];
    globalVars.forEach(varName => {
        if (window[varName] !== undefined) {
            console.log(`🎯 找到全局变量 ${varName}:`, typeof window[varName]);
            if (typeof window[varName] === 'object') {
                const obj = window[varName];
                if (obj && obj.antiContent) {
                    console.log('🎉 在全局变量中找到Anti-Content:', obj.antiContent);
                    debugState.antiContentFound = obj.antiContent;
                }
            }
        }
    });
}

// 4. 触发API请求
function triggerApiRequests() {
    console.log('🚀 尝试触发API请求...');
    
    // 模拟用户操作
    const clickableElements = [
        'button',
        'a[href*="seller"]',
        '.menu-item',
        '[role="menuitem"]',
        '.nav-item'
    ];
    
    clickableElements.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log(`🖱️ 找到可点击元素 ${selector}:`, elements.length);
    });
    
    // 尝试刷新页面数据
    if (window.location.href.includes('seller.kuajingmaihuo.com')) {
        console.log('🔄 尝试刷新页面...');
        // 可以尝试按F5或者点击刷新按钮
    }
}

// 5. 生成报告
function generateReport() {
    console.log('📊 生成调试报告...');
    
    const report = {
        timestamp: new Date().toLocaleString(),
        pageUrl: window.location.href,
        interceptedRequests: debugState.interceptedRequests.length,
        antiContentFound: debugState.antiContentFound,
        mallIdFound: debugState.mallIdFound,
        localStorage: {
            antiContent: localStorage.getItem('debug_anti_content'),
            mallId: localStorage.getItem('debug_mall_id')
        },
        earlyInterceptor: window.temuEarlyInterceptor ? 'Found' : 'Not Found',
        requests: debugState.interceptedRequests
    };
    
    console.log('📊 调试报告:', report);
    return report;
}

// 主函数
function runComprehensiveDebug() {
    console.log('🚀 开始全面调试...');
    
    // 1. 设置拦截
    setupComprehensiveInterception();
    
    // 2. 设置监控
    setupPageMonitoring();
    
    // 3. 立即检查
    checkForAntiContent();
    
    // 4. 尝试触发请求
    setTimeout(triggerApiRequests, 2000);
    
    // 5. 定期检查
    setInterval(checkForAntiContent, 10000);
    
    // 6. 定期生成报告
    setInterval(generateReport, 30000);
    
    console.log('✅ 全面调试设置完成');
    console.log('💡 请在页面中进行一些操作（点击菜单、刷新数据等）');
    console.log('💡 10分钟后运行 generateReport() 查看结果');
}

// 暴露全局函数
window.comprehensiveDebug = {
    run: runComprehensiveDebug,
    check: checkForAntiContent,
    trigger: triggerApiRequests,
    report: generateReport,
    state: debugState
};

console.log('🔧 全面调试工具已准备就绪！');
console.log('💡 运行 comprehensiveDebug.run() 开始调试');
console.log('💡 运行 comprehensiveDebug.report() 查看报告');

// 自动开始
runComprehensiveDebug();
