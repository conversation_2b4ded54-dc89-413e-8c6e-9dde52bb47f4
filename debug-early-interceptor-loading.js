// 调试早期拦截器加载问题
// 在Temu商家后台页面控制台运行

console.log('🔍 调试早期拦截器加载问题...');

window.debugEarlyInterceptor = {
    // 1. 检查扩展是否已加载
    checkExtensionStatus: function() {
        console.log('📦 检查扩展状态...');
        
        // 检查是否有扩展注入的脚本
        const scripts = Array.from(document.querySelectorAll('script'));
        const extensionScripts = scripts.filter(script => 
            script.src && script.src.includes('chrome-extension')
        );
        
        console.log('🔗 找到扩展脚本数量:', extensionScripts.length);
        extensionScripts.forEach((script, index) => {
            console.log(`脚本 ${index + 1}:`, script.src);
        });
        
        // 检查 window 对象上的扩展相关属性
        const extensionProps = [];
        for (const prop in window) {
            if (prop.toLowerCase().includes('temu') || 
                prop.toLowerCase().includes('early') ||
                prop.toLowerCase().includes('interceptor')) {
                extensionProps.push(prop);
            }
        }
        
        console.log('🌐 Window对象上的相关属性:', extensionProps);
        
        return {
            extensionScripts: extensionScripts.length,
            windowProps: extensionProps
        };
    },

    // 2. 检查控制台日志
    checkConsoleMessages: function() {
        console.log('📝 查看控制台是否有早期拦截器的日志...');
        console.log('💡 请查看控制台是否有以下消息:');
        console.log('   - [Early Interceptor] 🚀 启动终极拦截器');
        console.log('   - [Early Interceptor] ✅ 初始化完成');
        console.log('   - [Early Interceptor] ✅ 全局接口已暴露');
        
        // 尝试手动执行早期拦截器的部分逻辑
        this.manuallyInitializeInterceptor();
    },

    // 3. 手动初始化拦截器（备用方案）
    manuallyInitializeInterceptor: function() {
        console.log('🔧 尝试手动初始化拦截器...');
        
        if (window.temuEarlyInterceptor) {
            console.log('✅ 早期拦截器已存在，无需手动初始化');
            return;
        }
        
        console.log('⚙️ 手动设置fetch拦截器...');
        
        // 手动设置简化版的fetch拦截器
        const originalFetch = window.fetch;
        let interceptorActive = false;
        
        window.fetch = function(...args) {
            const [url, options] = args;
            const urlString = typeof url === 'string' ? url : url.toString();

            if (urlString.includes('seller.kuajingmaihuo.com')) {
                interceptorActive = true;
                console.log('🎯 [Manual Interceptor] 拦截fetch请求:', urlString);
                console.log('📋 [Manual Interceptor] 请求选项:', options);

                if (options && options.headers) {
                    const headers = options.headers;
                    console.log('🔍 [Manual Interceptor] 检查请求头:', headers);
                    
                    // 检查anti-content
                    for (const [key, value] of Object.entries(headers)) {
                        const lowerKey = key.toLowerCase();
                        if (lowerKey.includes('anti') && lowerKey.includes('content')) {
                            console.log('🎉 [Manual Interceptor] 找到 anti-content:', value);
                            
                            // 保存到localStorage
                            try {
                                localStorage.setItem('manual_anti_content', value);
                                localStorage.setItem('manual_anti_content_time', Date.now().toString());
                                console.log('✅ [Manual Interceptor] 已保存 anti-content');
                            } catch (e) {
                                console.warn('[Manual Interceptor] 保存失败:', e);
                            }
                        }
                        
                        if (lowerKey.includes('mall') && lowerKey.includes('id')) {
                            console.log('🎉 [Manual Interceptor] 找到 mallId:', value);
                            
                            try {
                                localStorage.setItem('manual_mall_id', value);
                                localStorage.setItem('manual_mall_id_time', Date.now().toString());
                                console.log('✅ [Manual Interceptor] 已保存 mallId');
                            } catch (e) {
                                console.warn('[Manual Interceptor] 保存mallId失败:', e);
                            }
                        }
                    }
                }
            }

            return originalFetch.apply(this, args);
        };
        
        // 创建简化的全局接口
        window.temuEarlyInterceptor = {
            getCachedAntiContent: () => localStorage.getItem('manual_anti_content'),
            getCachedMallId: () => localStorage.getItem('manual_mall_id'),
            getState: () => ({
                antiContentFound: localStorage.getItem('manual_anti_content'),
                mallIdFound: localStorage.getItem('manual_mall_id'),
                interceptedRequests: []
            }),
            report: () => {
                console.log('📊 手动拦截器报告:', {
                    antiContent: localStorage.getItem('manual_anti_content'),
                    mallId: localStorage.getItem('manual_mall_id')
                });
            },
            clearCache: () => {
                localStorage.removeItem('manual_anti_content');
                localStorage.removeItem('manual_mall_id');
            }
        };
        
        console.log('✅ 手动拦截器设置完成');
        console.log('💡 现在请在页面中进行一些操作，触发API请求');
        
        return interceptorActive;
    },

    // 4. 检查manifest配置
    checkManifestConfig: function() {
        console.log('📜 检查manifest配置建议...');
        console.log('请确认以下配置:');
        console.log('1. manifest.config.ts 中 early-interceptor.ts 只配置一次');
        console.log('2. run_at 设置为 "document_start"');
        console.log('3. matches 包含当前页面URL');
        console.log('4. 扩展已重新构建和安装');
        
        console.log('当前页面URL:', window.location.href);
        
        const temuUrls = [
            "*://seller.temu.com/*",
            "*://seller.kuajingmaihuo.com/*",
            "*://seller-cn.temu.com/*"
        ];
        
        console.log('应该匹配的URL模式:', temuUrls);
    },

    // 5. 运行完整诊断
    runDiagnosis: function() {
        console.log('🏥 运行完整诊断...');
        
        const extensionStatus = this.checkExtensionStatus();
        this.checkConsoleMessages();
        this.checkManifestConfig();
        
        console.log('\n📊 诊断总结:');
        console.log('扩展脚本数量:', extensionStatus.extensionScripts);
        console.log('Window属性数量:', extensionStatus.windowProps.length);
        console.log('早期拦截器状态:', window.temuEarlyInterceptor ? '✅ 存在' : '❌ 不存在');
        
        if (!window.temuEarlyInterceptor) {
            console.log('\n🔧 建议的修复步骤:');
            console.log('1. 检查扩展是否正确安装和启用');
            console.log('2. 检查manifest配置是否正确');
            console.log('3. 重新构建和安装扩展');
            console.log('4. 刷新页面重新测试');
            console.log('5. 运行 debugEarlyInterceptor.manuallyInitializeInterceptor() 作为临时方案');
        }
        
        return {
            extensionScripts: extensionStatus.extensionScripts,
            windowProps: extensionStatus.windowProps,
            earlyInterceptorExists: !!window.temuEarlyInterceptor
        };
    }
};

console.log('✅ 调试工具准备完成！');
console.log('💡 运行 debugEarlyInterceptor.runDiagnosis() 开始诊断');
console.log('💡 运行 debugEarlyInterceptor.manuallyInitializeInterceptor() 手动设置拦截器');
