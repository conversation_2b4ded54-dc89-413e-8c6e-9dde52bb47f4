// Anti-Content 调试脚本
// 在 Temu 商家后台页面的控制台中运行此脚本

console.log('🔍 开始调试 Anti-Content 获取...');

// 调试函数
function debugAntiContent() {
    console.log('=== Anti-Content 调试开始 ===');
    
    // 1. 检查页面URL
    console.log('当前页面URL:', window.location.href);
    
    // 2. 检查页面标题
    console.log('页面标题:', document.title);
    
    // 3. 检查所有meta标签
    const allMetas = document.querySelectorAll('meta');
    console.log('所有meta标签数量:', allMetas.length);
    allMetas.forEach((meta, index) => {
        const name = meta.getAttribute('name');
        const property = meta.getAttribute('property');
        const content = meta.getAttribute('content');
        if (name || property) {
            console.log(`Meta ${index + 1}:`, { name, property, content: content?.substring(0, 100) });
        }
    });
    
    // 4. 检查localStorage中的所有键
    console.log('localStorage键列表:');
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
            const value = localStorage.getItem(key);
            console.log(`localStorage[${key}]:`, value?.substring(0, 100));
        }
    }
    
    // 5. 检查sessionStorage中的所有键
    console.log('sessionStorage键列表:');
    for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key) {
            const value = sessionStorage.getItem(key);
            console.log(`sessionStorage[${key}]:`, value?.substring(0, 100));
        }
    }
    
    // 6. 检查window对象的关键属性
    const keyProps = ['__ANTI_CONTENT__', '__INITIAL_STATE__', 'g_config', '__APP_STATE__', '__TEMU_STATE__'];
    keyProps.forEach(prop => {
        if (window[prop] !== undefined) {
            console.log(`window.${prop}:`, typeof window[prop], window[prop]);
        }
    });
    
    // 7. 搜索script标签中的anti-content
    const scripts = document.querySelectorAll('script');
    console.log('找到script标签数量:', scripts.length);
    
    let foundAntiContent = null;
    let scriptIndex = 0;
    
    for (const script of scripts) {
        const content = script.textContent || script.innerHTML;
        if (!content) continue;
        
        scriptIndex++;
        
        // 尝试多种模式匹配
        const patterns = [
            /anti-content['"]\s*:\s*['"]([^'"]+)['"]/i,
            /"anti-content"\s*:\s*"([^"]+)"/i,
            /'anti-content'\s*:\s*'([^']+)'/i,
            /antiContent['"]\s*:\s*['"]([^'"]+)['"]/i,
            /ANTI_CONTENT['"]\s*:\s*['"]([^'"]+)['"]/i,
            /["']anti-content["']\s*:\s*["']([^"']+)["']/i
        ];

        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match) {
                console.log(`✅ 在第${scriptIndex}个script标签中找到 anti-content:`, match[1]);
                foundAntiContent = match[1];
                break;
            }
        }
        
        // 如果script内容包含anti-content关键词，输出调试信息
        if (content.toLowerCase().includes('anti-content') || content.toLowerCase().includes('anticontent')) {
            console.log(`第${scriptIndex}个script包含anti-content关键词，内容片段:`, content.substring(0, 500));
        }
        
        if (foundAntiContent) break;
    }
    
    // 8. 检查网络请求
    console.log('检查最近的网络请求...');
    const entries = performance.getEntriesByType('resource');
    const temuRequests = entries.filter(entry => entry.name.includes('seller.kuajingmaihuo.com'));
    console.log('找到Temu相关请求:', temuRequests.length);
    temuRequests.forEach((entry, index) => {
        console.log(`请求 ${index + 1}:`, entry.name);
    });
    
    // 9. 尝试拦截fetch请求
    console.log('设置fetch拦截...');
    const originalFetch = window.fetch;
    let interceptedAntiContent = null;
    
    window.fetch = function(...args) {
        const [url, options] = args;
        if (typeof url === 'string' && url.includes('seller.kuajingmaihuo.com')) {
            console.log('拦截到Temu请求:', url);
            const headers = options?.headers;
            if (headers && headers['anti-content']) {
                interceptedAntiContent = headers['anti-content'];
                console.log('✅ 从fetch请求中拦截到 anti-content:', interceptedAntiContent);
            }
            console.log('请求头:', headers);
        }
        return originalFetch.apply(this, args);
    };
    
    // 10秒后恢复原始fetch
    setTimeout(() => {
        window.fetch = originalFetch;
        console.log('fetch拦截已恢复');
    }, 10000);
    
    console.log('=== Anti-Content 调试结束 ===');
    
    if (foundAntiContent) {
        console.log('🎉 找到 anti-content:', foundAntiContent);
        return foundAntiContent;
    } else if (interceptedAntiContent) {
        console.log('🎉 拦截到 anti-content:', interceptedAntiContent);
        return interceptedAntiContent;
    } else {
        console.log('❌ 未找到 anti-content');
        return null;
    }
}

// 运行调试
const result = debugAntiContent();

// 提供一些有用的函数
window.debugAntiContent = debugAntiContent;

console.log('🔧 调试函数已添加到 window.debugAntiContent，可以随时调用');
console.log('💡 建议：在页面完全加载后，或者进行一些操作（如点击菜单）后再次运行 debugAntiContent()');

// 监听页面变化
let lastUrl = window.location.href;
setInterval(() => {
    if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        console.log('🔄 页面URL发生变化，重新运行调试...');
        setTimeout(() => {
            debugAntiContent();
        }, 2000);
    }
}, 1000);

console.log('🔄 已设置页面变化监听器');
