// 最终测试脚本 - 验证修复后的扩展是否能正确工作
// 在Temu商家后台页面控制台运行

console.log('🎯 最终测试脚本 - 验证扩展修复效果');

window.finalTest = {
    // 1. 检查拦截器状态
    checkInterceptors: function() {
        console.log('🔍 检查拦截器状态...');
        
        // 检查早期拦截器
        const earlyInterceptor = window.temuEarlyInterceptor;
        if (earlyInterceptor) {
            console.log('✅ 早期拦截器已加载');
            const cache = earlyInterceptor.getCache();
            console.log('💾 早期拦截器缓存:', {
                antiContent: cache.antiContent ? cache.antiContent.substring(0, 50) + '...' : null,
                mallId: cache.mallId
            });
        } else {
            console.log('❌ 早期拦截器未找到');
        }
        
        // 检查ultimate-debug拦截器
        if (window.ultimateDebug) {
            console.log('✅ ultimate-debug拦截器存在');
        } else {
            console.log('❌ ultimate-debug拦截器不存在');
        }
        
        return !!earlyInterceptor;
    },

    // 2. 检查localStorage缓存
    checkCache: function() {
        console.log('💾 检查localStorage缓存...');
        
        const cacheKeys = [
            'temu_cached_anti_content',
            'ultimate_anti_content',
            'temu_cached_mall_id',
            'ultimate_mall_id'
        ];
        
        const cacheData = {};
        let hasValidCache = false;
        
        cacheKeys.forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                cacheData[key] = key.includes('anti_content') ? 
                    value.substring(0, 50) + '...' : value;
                hasValidCache = true;
            } else {
                cacheData[key] = null;
            }
        });
        
        console.log('📋 缓存数据:', cacheData);
        
        if (hasValidCache) {
            console.log('✅ 找到有效缓存');
        } else {
            console.log('❌ 没有找到有效缓存');
        }
        
        return hasValidCache;
    },

    // 3. 等待系统自动请求
    waitForSystemRequests: function(timeoutSeconds = 60) {
        console.log(`⏰ 等待系统自动请求 (${timeoutSeconds}秒)...`);
        console.log('💡 请在页面中进行正常操作：点击菜单、查看商品等');
        
        return new Promise((resolve) => {
            const startTime = Date.now();
            const checkInterval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                
                if (elapsed % 10 === 0) { // 每10秒输出一次
                    console.log(`⏰ 已等待 ${elapsed}秒...`);
                }
                
                // 检查是否获取到缓存
                const hasCache = this.checkCache();
                if (hasCache) {
                    clearInterval(checkInterval);
                    console.log('🎉 检测到缓存数据！');
                    resolve(true);
                    return;
                }
                
                // 超时
                if (elapsed >= timeoutSeconds) {
                    clearInterval(checkInterval);
                    console.log('⏰ 等待超时');
                    resolve(false);
                }
            }, 1000);
        });
    },

    // 4. 测试扩展服务
    testExtensionServices: async function() {
        console.log('🔌 测试扩展服务...');
        
        try {
            // 检查Chrome扩展API
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                console.log('❌ Chrome扩展API不可用');
                return false;
            }
            
            // 尝试发送消息到background script
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'TEST_CONNECTION'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(response);
                    }
                });
            });
            
            console.log('✅ 扩展连接成功:', response);
            return true;
        } catch (error) {
            console.log('❌ 扩展连接失败:', error);
            return false;
        }
    },

    // 5. 模拟扩展的数据获取逻辑
    simulateExtensionDataRetrieval: function() {
        console.log('🎭 模拟扩展数据获取逻辑...');
        
        // 模拟TemuDataService的逻辑
        const getAntiContentFromStorage = () => {
            const sources = [
                'ultimate_anti_content',
                'temu_cached_anti_content'
            ];
            
            for (const source of sources) {
                const cached = localStorage.getItem(source);
                if (cached) {
                    console.log(`✅ 从 ${source} 获取到 anti-content`);
                    return cached;
                }
            }
            return null;
        };
        
        const getMallIdFromStorage = () => {
            const sources = [
                'ultimate_mall_id',
                'temu_cached_mall_id'
            ];
            
            for (const source of sources) {
                const cached = localStorage.getItem(source);
                if (cached) {
                    console.log(`✅ 从 ${source} 获取到 mallId`);
                    return cached;
                }
            }
            return null;
        };
        
        const antiContent = getAntiContentFromStorage();
        const mallId = getMallIdFromStorage();
        
        if (antiContent && mallId) {
            console.log('✅ 扩展数据获取模拟成功');
            console.log('🔑 anti-content:', antiContent.substring(0, 50) + '...');
            console.log('🏪 mallId:', mallId);
            return { antiContent, mallId };
        } else {
            console.log('❌ 扩展数据获取模拟失败');
            return null;
        }
    },

    // 6. 运行完整测试
    runFullTest: async function() {
        console.log('🚀 开始完整测试...');
        console.log('='.repeat(60));
        
        // 1. 检查拦截器
        console.log('1️⃣ 检查拦截器状态');
        const interceptorOk = this.checkInterceptors();
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 2. 检查初始缓存
        console.log('2️⃣ 检查初始缓存状态');
        let cacheOk = this.checkCache();
        
        if (!cacheOk) {
            console.log('\n' + '-'.repeat(40) + '\n');
            
            // 3. 等待系统请求
            console.log('3️⃣ 等待系统自动请求');
            console.log('💡 请在页面中进行操作：点击菜单、查看商品列表等');
            cacheOk = await this.waitForSystemRequests(120); // 等待2分钟
        }
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 4. 测试扩展服务
        console.log('4️⃣ 测试扩展服务');
        const extensionOk = await this.testExtensionServices();
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 5. 模拟扩展逻辑
        console.log('5️⃣ 模拟扩展数据获取');
        const dataOk = this.simulateExtensionDataRetrieval();
        
        console.log('\n' + '='.repeat(60));
        
        // 6. 生成测试报告
        const report = {
            timestamp: new Date().toLocaleString(),
            interceptor: interceptorOk,
            cache: cacheOk,
            extension: extensionOk,
            dataRetrieval: !!dataOk,
            overall: interceptorOk && cacheOk && !!dataOk
        };
        
        console.log('📊 测试报告:', report);
        
        if (report.overall) {
            console.log('🎉 测试通过！扩展应该能正常工作了');
            console.log('💡 现在可以在扩展UI中查看数据');
        } else {
            console.log('❌ 测试失败，需要进一步调试');
            
            if (!report.interceptor) {
                console.log('🔧 建议：重新加载扩展');
            }
            if (!report.cache) {
                console.log('🔧 建议：在页面中进行更多操作，等待系统发起API请求');
            }
            if (!report.extension) {
                console.log('🔧 建议：检查扩展是否正确安装');
            }
        }
        
        return report;
    }
};

console.log('🔧 最终测试工具已准备就绪！');
console.log('💡 运行 finalTest.runFullTest() 开始完整测试');
console.log('💡 运行 finalTest.checkCache() 检查缓存状态');
console.log('💡 运行 finalTest.waitForSystemRequests() 等待系统请求');

console.log('\n📝 重要说明：');
console.log('1. 确保已重新加载最新的扩展');
console.log('2. 在页面中进行正常操作（点击菜单、查看商品等）');
console.log('3. 等待系统自动发起包含anti-content的请求');
console.log('4. 扩展会自动拦截并缓存这些数据');

// 自动开始检查
finalTest.checkInterceptors();
finalTest.checkCache();
