// Sample code if using extensionpay.com
// import { extPay } from 'src/utils/payment/extPay'
// extPay.startBackground()

// 导入JSZip库以支持ZIP文件创建
import '../lib/jszip.min.js'

// 声明JSZip全局变量
declare const JSZip: any

chrome.runtime.onInstalled.addListener(async (opt) => {
  // Check if reason is install or update. Eg: opt.reason === 'install' // If extension is installed.
  // opt.reason === 'update' // If extension is updated.
  if (opt.reason === "install") {
    // 首次安装时，打开 side-panel 而不是 setup 页面
    try {
      // 尝试打开 side panel
      await chrome.sidePanel.open({ windowId: (await chrome.windows.getCurrent()).id })
    } catch (error) {
      // 如果 side panel 不可用，则打开 setup 页面
      chrome.tabs.create({
        active: true,
        url: chrome.runtime.getURL("src/ui/setup/index.html#/setup/install"),
      })
    }

    return
  }

  if (opt.reason === "update") {
    // 更新时，也打开 side-panel 而不是 setup 页面
    try {
      // 尝试打开 side panel
      await chrome.sidePanel.open({ windowId: (await chrome.windows.getCurrent()).id })
    } catch (error) {
      // 如果 side panel 不可用，则不做任何操作
      console.info('Side panel not available, skipping auto-open')
    }

    return
  }
})

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}

console.info("hello world from background")

// Temu 数据获取服务
class TemuBackgroundService {
  // 获取 Temu 店铺信息
  async getTemuShopInfo(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 开始获取 Temu 店铺信息...')

      // 1. 查找 Temu 商家后台标签页
      const temuTab = await this.findTemuSellerTab()
      if (!temuTab) {
        return {
          success: false,
          error: '未找到 Temu 商家后台标签页，请先打开并登录 Temu 商家后台'
        }
      }

      console.info('[Background] 找到 Temu 标签页:', temuTab.url)

      // 2. 向 content script 发送消息获取数据
      try {
        const response = await chrome.tabs.sendMessage(temuTab.id!, {
          action: 'GET_TEMU_INFO'
        })

        if (response && response.success) {
          console.info('[Background] 成功获取 Temu 信息:', response.data)
          return {
            success: true,
            data: response.data
          }
        } else {
          return {
            success: false,
            error: response?.error || '获取 Temu 信息失败'
          }
        }
      } catch (messageError) {
        console.warn('[Background] Content script 通信失败，尝试注入脚本...')

        // 3. 如果 content script 不可用，尝试注入脚本
        return await this.injectAndGetTemuInfo(temuTab.id!)
      }
    } catch (error) {
      console.error('[Background] 获取 Temu 店铺信息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 查找 Temu 商家后台标签页
  private async findTemuSellerTab(): Promise<chrome.tabs.Tab | null> {
    const tabs = await chrome.tabs.query({})

    const temuDomains = [
      'seller.temu.com',
      'seller.kuajingmaihuo.com',
      'seller-cn.temu.com',
      'agentseller.temu.com',
      'agentseller-us.temu.com'
    ]

    for (const tab of tabs) {
      if (tab.url) {
        const url = new URL(tab.url)
        if (temuDomains.some(domain => url.hostname.includes(domain))) {
          return tab
        }
      }
    }

    return null
  }

  // 注入脚本并获取 Temu 信息
  private async injectAndGetTemuInfo(tabId: number): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 注入脚本获取 Temu 信息...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.getTemuInfoFromPage
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功获取数据:', result.data)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 在页面中执行的函数（会被注入到 Temu 页面）
  private getTemuInfoFromPage(): { success: boolean; data?: any; error?: string } {
    try {
      console.info('[Injected Script] 开始获取 Temu 信息...')

      // 获取 anti-content 头部
      const getAntiContent = (): string | null => {
        try {
          // 方法1: 从全局变量获取
          const win = window as any
          if (win.antiContent) return win.antiContent
          if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

          // 方法2: 从脚本中搜索
          const scripts = Array.from(document.querySelectorAll('script'))
          for (const script of scripts) {
            const content = script.textContent || script.innerHTML
            const patterns = [
              /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
              /antiContent\s*:\s*["']([^"']+)["']/i,
              /"anti-content":\s*"([^"]+)"/i
            ]

            for (const pattern of patterns) {
              const match = content.match(pattern)
              if (match) return match[1]
            }
          }
          return null
        } catch (error) {
          console.warn('[Injected Script] 获取 anti-content 失败:', error)
          return null
        }
      }

      // 调用 Temu API
      const antiContent = getAntiContent()
      console.info('[Injected Script] Anti-content:', antiContent ? '已获取' : '未找到')

      const headers: Record<string, string> = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        'Cache-Control': 'max-age=0'
      }

      if (antiContent) {
        headers['anti-content'] = antiContent
      }

      // 返回 Promise（注意：这个函数会在页面上下文中执行）
      return fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST',
        credentials: 'include',
        headers,
        body: JSON.stringify({})
      })
      .then(response => response.json())
      .then(data => {
        console.info('[Injected Script] API 响应:', data)
        if (data.success && data.result) {
          return {
            success: true,
            data: data.result
          }
        } else {
          return {
            success: false,
            error: 'API 返回数据格式错误'
          }
        }
      })
      .catch(error => {
        console.error('[Injected Script] API 调用失败:', error)
        return {
          success: false,
          error: error.message || 'API 调用失败'
        }
      })
    } catch (error) {
      console.error('[Injected Script] 执行失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行失败'
      }
    }
  }
}

// Amazon数据提取服务
class AmazonBackgroundService {
  // 提取Amazon商品数据
  async extractAmazonProductData(url: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 开始提取Amazon商品数据:', url)

      // 1. 查找Amazon标签页或创建新标签页
      const amazonTab = await this.findOrCreateAmazonTab(url)
      if (!amazonTab) {
        return {
          success: false,
          error: '无法打开Amazon商品页面'
        }
      }

      console.info('[Background] 找到Amazon标签页:', amazonTab.url)

      // 2. 等待页面加载完成
      await this.waitForPageLoad(amazonTab.id!)

      // 3. 向content script发送消息提取数据
      try {
        const response = await chrome.tabs.sendMessage(amazonTab.id!, {
          action: 'EXTRACT_AMAZON_DATA'
        })

        if (response && response.success) {
          console.info('[Background] 成功提取Amazon数据:', response.data)
          return response
        } else {
          return {
            success: false,
            error: response?.error || '提取Amazon数据失败'
          }
        }
      } catch (messageError) {
        console.warn('[Background] Content script通信失败，尝试注入脚本...')

        // 4. 如果content script不可用，尝试注入脚本
        return await this.injectAndExtractData(amazonTab.id!)
      }
    } catch (error) {
      console.error('[Background] 提取Amazon数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 查找或创建Amazon标签页
  private async findOrCreateAmazonTab(url: string): Promise<chrome.tabs.Tab | null> {
    try {
      // 首先查找是否已有相同URL的标签页
      const tabs = await chrome.tabs.query({})
      const existingTab = tabs.find(tab => tab.url === url)

      if (existingTab) {
        // 激活现有标签页
        await chrome.tabs.update(existingTab.id!, { active: true })
        return existingTab
      }

      // 创建新标签页
      const newTab = await chrome.tabs.create({
        url: url,
        active: false // 在后台打开，不干扰用户
      })

      return newTab
    } catch (error) {
      console.error('[Background] 创建Amazon标签页失败:', error)
      return null
    }
  }

  // 等待页面加载完成
  private async waitForPageLoad(tabId: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('页面加载超时'))
      }, 30000) // 30秒超时

      const checkStatus = () => {
        chrome.tabs.get(tabId, (tab) => {
          if (chrome.runtime.lastError) {
            clearTimeout(timeout)
            reject(new Error(chrome.runtime.lastError.message))
            return
          }

          if (tab.status === 'complete') {
            clearTimeout(timeout)
            // 额外等待2秒确保页面完全加载
            setTimeout(resolve, 2000)
          } else {
            setTimeout(checkStatus, 500)
          }
        })
      }

      checkStatus()
    })
  }

  // 注入脚本并提取数据
  private async injectAndExtractData(tabId: number): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 注入脚本提取Amazon数据...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.extractDataFromPage
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功提取数据:', result.data)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 在页面中执行的数据提取函数
  private extractDataFromPage(): { success: boolean; data?: any; error?: string } {
    try {
      console.info('[Injected Script] 开始提取Amazon商品数据...')

      // 检查是否为Amazon产品页面
      if (!window.location.href.includes('amazon.com') ||
          (!window.location.href.includes('/dp/') && !window.location.href.includes('/gp/product/'))) {
        return {
          success: false,
          error: '当前页面不是Amazon产品页面'
        }
      }

      // 提取ASIN
      const extractAsin = (): string => {
        const url = window.location.href
        const match = url.match(/\/dp\/([A-Z0-9]{10})/)
        return match ? match[1] : 'UNKNOWN'
      }

      // 提取标题
      const extractTitle = (): string => {
        const titleElement = document.querySelector('#productTitle')
        return titleElement?.textContent?.trim() || ''
      }

      // 提取品牌
      const extractBrand = (): string | undefined => {
        const brandElement = document.querySelector('#bylineInfo')
        if (brandElement) {
          const brandText = brandElement.textContent?.trim() || ''
          const brandMatch = brandText.match(/Visit the (.+?) Store/)
          return brandMatch ? brandMatch[1] : brandText
        }
        return undefined
      }

      // 提取价格
      const extractPrice = (): number | undefined => {
        const priceElement = document.querySelector('.a-price .a-offscreen')
        if (priceElement) {
          const priceText = priceElement.textContent?.trim() || ''
          const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/)
          if (priceMatch) {
            return parseFloat(priceMatch[0].replace(',', ''))
          }
        }
        return undefined
      }

      // 提取评分
      const extractRating = (): number | undefined => {
        const ratingElement = document.querySelector('[data-hook="average-star-rating"] .a-icon-alt')
        if (ratingElement) {
          const ratingText = ratingElement.textContent?.trim() || ''
          const ratingMatch = ratingText.match(/(\d+\.?\d*)/)
          return ratingMatch ? parseFloat(ratingMatch[1]) : undefined
        }
        return undefined
      }

      // 提取评论数
      const extractReviewCount = (): number | undefined => {
        const reviewElement = document.querySelector('[data-hook="total-review-count"]')
        if (reviewElement) {
          const reviewText = reviewElement.textContent?.trim() || ''
          const reviewMatch = reviewText.replace(/,/g, '').match(/(\d+)/)
          return reviewMatch ? parseInt(reviewMatch[1]) : undefined
        }
        return undefined
      }

      // 提取主图
      const extractMainImage = (): string | undefined => {
        const mainImageElement = document.querySelector('#landingImage, #imgBlkFront') as HTMLImageElement
        return mainImageElement?.src || mainImageElement?.getAttribute('data-src') || undefined
      }

      // 提取图片列表
      const extractImageUrls = (): string[] => {
        const imageUrls: string[] = []
        const imageElements = document.querySelectorAll('#altImages img, .imageThumbnail img') as NodeListOf<HTMLImageElement>

        imageElements.forEach(img => {
          const imgUrl = img.src || img.getAttribute('data-src')
          if (imgUrl && imgUrl.includes('amazon.com') && !imgUrl.includes('data:image')) {
            imageUrls.push(imgUrl)
          }
        })

        return imageUrls.slice(0, 10) // 限制最多10张图片
      }

      // 提取要点
      const extractBulletPoints = (): string[] => {
        const bulletPoints: string[] = []
        const bulletElements = document.querySelectorAll('#featurebullets_feature_div li span.a-list-item')

        bulletElements.forEach(bullet => {
          const text = bullet.textContent?.trim() || ''
          if (text && text.length > 10 && !text.includes('Make sure')) {
            bulletPoints.push(text)
          }
        })

        return bulletPoints.slice(0, 5) // 限制最多5个要点
      }

      // 提取规格参数
      const extractSpecifications = (): Record<string, string> => {
        const specs: Record<string, string> = {}
        const specTables = document.querySelectorAll('#productDetails_techSpec_section_1 tr, #productDetails_detailBullets_sections1 tr')

        specTables.forEach(row => {
          const cells = row.querySelectorAll('td')
          if (cells.length >= 2) {
            const key = cells[0].textContent?.trim() || ''
            const value = cells[1].textContent?.trim() || ''
            if (key && value) {
              specs[key] = value
            }
          }
        })

        return specs
      }

      // 组装数据
      const amazonData = {
        asin: extractAsin(),
        title: extractTitle(),
        brand: extractBrand(),
        price: extractPrice(),
        currency: 'USD',
        rating: extractRating(),
        reviewCount: extractReviewCount(),
        mainImageUrl: extractMainImage(),
        imageUrls: extractImageUrls(),
        bulletPoints: extractBulletPoints(),
        description: extractBulletPoints().join(' '),
        categoryPath: '',
        stockStatus: 'In Stock',
        specifications: extractSpecifications(),
        variations: [],
        sourceUrl: window.location.href
      }

      console.info('[Injected Script] Amazon数据提取完成:', amazonData)

      return {
        success: true,
        data: amazonData
      }
    } catch (error) {
      console.error('[Injected Script] 提取Amazon数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '数据提取失败'
      }
    }
  }
}

// 店小秘后台服务
class DianxiaomiBackgroundService {
  // 调用店小秘API
  async callDianxiaomiAPI(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 调用店小秘API:', apiConfig.url)

      // 1. 查找店小秘标签页
      const dxmTab = await this.findDianxiaomiTab()
      if (!dxmTab) {
        return {
          success: false,
          error: '未找到店小秘标签页，请先打开并登录店小秘网站'
        }
      }

      console.info('[Background] 找到店小秘标签页:', dxmTab.url)

      // 2. 向 content script 发送消息调用API
      try {
        const response = await chrome.tabs.sendMessage(dxmTab.id!, {
          action: 'CALL_DIANXIAOMI_API',
          apiConfig
        })

        if (response && response.success) {
          console.info('[Background] 店小秘API调用成功:', response.data)
          return response
        } else {
          return {
            success: false,
            error: response?.error || '店小秘API调用失败'
          }
        }
      } catch (messageError) {
        console.warn('[Background] Content script 通信失败，尝试注入脚本...')

        // 3. 如果 content script 不可用，尝试注入脚本
        return await this.injectAndCallAPI(dxmTab.id!, apiConfig)
      }
    } catch (error) {
      console.error('[Background] 调用店小秘API失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  // 查找店小秘标签页
  private async findDianxiaomiTab(): Promise<chrome.tabs.Tab | null> {
    const tabs = await chrome.tabs.query({})

    const dxmDomains = [
      'dianxiaomi.com',
      'www.dianxiaomi.com'
    ]

    for (const tab of tabs) {
      if (tab.url) {
        const url = new URL(tab.url)
        if (dxmDomains.some(domain => url.hostname.includes(domain))) {
          return tab
        }
      }
    }

    return null
  }

  // 注入脚本并调用API
  private async injectAndCallAPI(tabId: number, apiConfig: any): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Background] 注入脚本调用店小秘API...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.callAPIFromPage,
        args: [apiConfig]
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功调用API:', result.data)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 在页面中执行的函数（会被注入到店小秘页面）
  private async callAPIFromPage(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[Injected Script] 调用店小秘API:', apiConfig.url)

      const defaultHeaders = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }

      const headers = { ...defaultHeaders, ...apiConfig.headers }
      const method = apiConfig.method || 'GET'

      const fetchOptions: RequestInit = {
        method,
        credentials: 'include',
        headers
      }

      if (method !== 'GET' && apiConfig.data) {
        fetchOptions.body = JSON.stringify(apiConfig.data)
      }

      const response = await fetch(apiConfig.url, fetchOptions)

      console.info('[Injected Script] API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[Injected Script] API响应数据:', data)

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('[Injected Script] API调用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API调用失败'
      }
    }
  }

  // 获取店小秘Token状态
  async getDianxiaomiTokenStatus(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/userInfo.json',
      method: 'GET'
    })
  }

  // 获取店铺列表
  async getShopList(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/shop/list/pddkj.htm',
      method: 'GET'
    })
  }

  // 获取运费模板
  async getFreightTemplates(shopId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/freight/template/list.json',
      method: 'POST',
      data: { shopId }
    })
  }

  // 获取商品分类
  async getProductCategories(shopId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/category/list.json',
      method: 'POST',
      data: { shopId }
    })
  }

  // 上传商品
  async uploadProduct(productData: any): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/product/upload.json',
      method: 'POST',
      data: productData
    })
  }

  // 创建ZIP文件 - 现在可以在background中使用JSZip了
  async createZipFile(jsonData: string): Promise<{ success: boolean; zipBlob?: Blob; size?: number; error?: string }> {
    try {
      console.info('[Background] 开始创建ZIP文件...')

      // 检查JSZip是否可用
      if (typeof JSZip === 'undefined') {
        throw new Error('JSZip库不可用')
      }

      // 创建ZIP实例
      const zip = new JSZip()

      // 将JSON数据添加到ZIP中作为choiceSave.txt
      zip.file('choiceSave.txt', jsonData)

      console.info('[Background] 正在生成ZIP文件...')

      // 生成ZIP文件
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      })

      console.info(`[Background] ZIP文件创建成功，大小: ${zipBlob.size} bytes`)

      return {
        success: true,
        zipBlob,
        size: zipBlob.size
      }
    } catch (error) {
      console.error('[Background] 创建ZIP文件失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'ZIP文件创建失败'
      }
    }
  }

  // 上传店小秘商品 - 完全在background中执行，不需要注入脚本
  async uploadDianxiaomiProduct(jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Background] 开始上传店小秘商品...')
      console.info('[Background] JSON数据大小:', jsonData.length, 'bytes')
      console.info('[Background] 使用简化流程：Background创建ZIP → Background直接上传')

      // 1. 查找店小秘标签页（用于获取Cookie）
      const dxmTab = await this.findDianxiaomiTab()
      if (!dxmTab) {
        return {
          success: false,
          error: '未找到店小秘标签页，请先打开并登录店小秘网站'
        }
      }

      console.info('[Background] 找到店小秘标签页:', dxmTab.url)

      // 2. 在background中创建ZIP文件
      console.info('[Background] 在background中创建ZIP文件...')
      const zipResult = await this.createZipFile(jsonData)
      if (!zipResult.success) {
        return {
          success: false,
          error: `ZIP文件创建失败: ${zipResult.error}`
        }
      }

      console.info(`[Background] ZIP文件创建成功，大小: ${zipResult.size} bytes`)

      // 3. 在background中直接上传ZIP文件
      console.info('[Background] 在background中直接上传ZIP文件...')
      return await this.uploadZipDirectly(zipResult.zipBlob!)
    } catch (error) {
      console.error('[Background] 上传店小秘商品失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  // 注入脚本并上传商品
  private async injectAndUploadProduct(tabId: number, jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Background] 注入脚本上传店小秘商品...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.uploadProductFromPage,
        args: [jsonData]
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功上传商品:', result)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 在background中直接上传ZIP文件
  async uploadZipDirectly(zipBlob: Blob): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Background] 开始直接上传ZIP文件...')
      console.info('[Background] ZIP文件大小:', zipBlob.size, 'bytes')

      // 创建FormData - 完全按照test_upload.html的方式
      const formData = new FormData()
      formData.append('file', zipBlob, 'blob')
      formData.append('op', '1')

      console.info('[Background] 发送上传请求...')

      // 发送请求 - 完全按照test_upload.html的方式
      const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Origin': 'https://www.dianxiaomi.com',
          'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      })

      console.info('[Background] 响应状态:', response.status, response.statusText)

      const responseText = await response.text()
      console.info('[Background] 响应内容:', responseText)

      // 收集响应头
      const headers: Record<string, string> = {}
      for (const [key, value] of response.headers.entries()) {
        headers[key] = value
      }

      let responseData: any = responseText
      try {
        responseData = JSON.parse(responseText)
      } catch (e) {
        // 如果不是JSON，保持原文本
      }

      return {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error) {
      console.error('[Background] 直接上传ZIP失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  // 注入脚本并上传ZIP文件（ZIP已在background中创建）- 已废弃，使用uploadZipDirectly
  private async injectAndUploadZipFile(tabId: number, zipBlob: Blob): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Background] 注入脚本上传ZIP文件...')
      console.info('[Background] ZIP文件大小:', zipBlob.size, 'bytes')

      // 将Blob转换为ArrayBuffer，以便传递给注入脚本
      const arrayBuffer = await zipBlob.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)

      // 注入脚本到页面 - 使用简化的同步包装
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: (zipData: number[]) => {
          // 在页面环境中执行的函数
          return new Promise(async (resolve) => {
            try {
              console.info('[Injected Script] 开始上传ZIP文件...')
              console.info('[Injected Script] ZIP数据大小:', zipData.length, 'bytes')

              // 将数组转换回Blob
              const uint8Array = new Uint8Array(zipData)
              const zipBlob = new Blob([uint8Array], { type: 'application/zip' })

              console.info('[Injected Script] ZIP Blob创建成功，大小:', zipBlob.size, 'bytes')

              // 创建FormData - 完全按照test_upload.html的方式
              const formData = new FormData()
              formData.append('file', zipBlob, 'blob')
              formData.append('op', '1')

              console.info('[Injected Script] 发送上传请求...')

              // 发送请求 - 完全按照test_upload.html的方式
              const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
                method: 'POST',
                body: formData,
                credentials: 'include',
                headers: {
                  'Accept': 'application/json, text/plain, */*',
                  'Accept-Language': 'zh-CN,zh;q=0.9',
                  'Origin': 'https://www.dianxiaomi.com',
                  'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
                  'Sec-Fetch-Dest': 'empty',
                  'Sec-Fetch-Mode': 'cors',
                  'Sec-Fetch-Site': 'same-origin',
                  'User-Agent': navigator.userAgent
                }
              })

              console.info('[Injected Script] 响应状态:', response.status, response.statusText)

              const responseText = await response.text()
              console.info('[Injected Script] 响应内容:', responseText)

              // 收集响应头
              const headers: any = {}
              for (const [key, value] of response.headers.entries()) {
                headers[key] = value
              }

              let responseData: any = responseText
              try {
                responseData = JSON.parse(responseText)
              } catch (e) {
                // 如果不是JSON，保持原文本
              }

              resolve({
                success: response.ok,
                data: responseData,
                status: response.status,
                headers,
                error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
              })
            } catch (error) {
              console.error('[Injected Script] 上传ZIP失败:', error)
              resolve({
                success: false,
                error: error instanceof Error ? error.message : '上传失败'
              })
            }
          })
        },
        args: [Array.from(uint8Array)] // 转换为普通数组以便传递
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[Background] 注入脚本成功上传ZIP:', result)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[Background] 注入脚本上传ZIP失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  // 包装函数，用于在注入脚本中处理异步上传
  private uploadZipFromPageWrapper(zipData: number[]) {
    // 这个函数会在页面环境中执行，返回一个立即执行的异步函数
    const asyncUpload = async () => {
      try {
        console.info('[Injected Script] 开始上传ZIP文件...')
        console.info('[Injected Script] ZIP数据大小:', zipData.length, 'bytes')

        // 将数组转换回Blob
        const uint8Array = new Uint8Array(zipData)
        const zipBlob = new Blob([uint8Array], { type: 'application/zip' })

        console.info('[Injected Script] ZIP Blob创建成功，大小:', zipBlob.size, 'bytes')

        // 创建FormData - 完全按照test_upload.html的方式
        const formData = new FormData()
        formData.append('file', zipBlob, 'blob')
        formData.append('op', '1')

        console.info('[Injected Script] 发送上传请求...')

        // 发送请求 - 完全按照test_upload.html的方式
        const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
          method: 'POST',
          body: formData,
          credentials: 'include',
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Origin': 'https://www.dianxiaomi.com',
            'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': navigator.userAgent
          }
        })

        console.info('[Injected Script] 响应状态:', response.status, response.statusText)

        const responseText = await response.text()
        console.info('[Injected Script] 响应内容:', responseText)

        // 收集响应头
        const headers = {}
        for (const [key, value] of response.headers.entries()) {
          headers[key] = value
        }

        let responseData = responseText
        try {
          responseData = JSON.parse(responseText)
        } catch (e) {
          // 如果不是JSON，保持原文本
        }

        return {
          success: response.ok,
          data: responseData,
          status: response.status,
          headers,
          error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
        }
      } catch (error) {
        console.error('[Injected Script] 上传ZIP失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '上传失败'
        }
      }
    }

    // 返回立即执行的异步函数
    return asyncUpload()
  }

  // 在页面中执行的ZIP上传函数（ZIP已创建，直接上传）- 这个函数已废弃，使用uploadZipFromPageWrapper
  private async uploadZipFromPage(zipData: number[]): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Injected Script] 开始上传ZIP文件...')
      console.info('[Injected Script] ZIP数据大小:', zipData.length, 'bytes')

      // 将数组转换回Blob
      const uint8Array = new Uint8Array(zipData)
      const zipBlob = new Blob([uint8Array], { type: 'application/zip' })

      console.info('[Injected Script] ZIP Blob创建成功，大小:', zipBlob.size, 'bytes')

      // 创建FormData - 完全按照test_upload.html的方式
      const formData = new FormData()
      formData.append('file', zipBlob, 'blob')
      formData.append('op', '1')

      console.info('[Injected Script] 发送上传请求...')

      // 发送请求 - 完全按照test_upload.html的方式
      const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Origin': 'https://www.dianxiaomi.com',
          'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[Injected Script] 响应状态:', response.status, response.statusText)

      const responseText = await response.text()
      console.info('[Injected Script] 响应内容:', responseText)

      // 收集响应头
      const headers: Record<string, string> = {}
      for (const [key, value] of response.headers.entries()) {
        headers[key] = value
      }

      let responseData: any = responseText
      try {
        responseData = JSON.parse(responseText)
      } catch (e) {
        // 如果不是JSON，保持原文本
      }

      return {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error) {
      console.error('[Injected Script] 上传ZIP失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  // 在页面中执行的上传函数（会被注入到店小秘页面）
  private async uploadProductFromPage(jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[Injected Script] 开始上传店小秘商品...')
      console.info('[Injected Script] JSON数据大小:', jsonData.length, 'bytes')

      // 检查并加载JSZip库
      let JSZip = (window as any).JSZip
      if (!JSZip) {
        console.info('[Injected Script] JSZip库不可用，尝试动态加载...')

        // 尝试动态加载JSZip
        try {
          const script = document.createElement('script')
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js'
          document.head.appendChild(script)

          // 等待脚本加载
          await new Promise((resolve, reject) => {
            script.onload = resolve
            script.onerror = reject
            setTimeout(reject, 10000) // 10秒超时
          })

          JSZip = (window as any).JSZip
          if (!JSZip) {
            throw new Error('JSZip加载后仍不可用')
          }

          console.info('[Injected Script] JSZip库动态加载成功')
        } catch (loadError) {
          throw new Error(`JSZip库加载失败: ${loadError}。请确保网络连接正常或页面已预加载JSZip库`)
        }
      }

      const zip = new JSZip()
      zip.file('choiceSave.txt', jsonData)

      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      })

      console.info('[Injected Script] ZIP文件创建成功，大小:', zipBlob.size, 'bytes')

      // 创建FormData
      const formData = new FormData()
      formData.append('file', zipBlob, 'blob')
      formData.append('op', '1')

      console.info('[Injected Script] 发送上传请求...')

      // 发送请求 - 完全按照test_upload.html的方式
      const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Origin': 'https://www.dianxiaomi.com',
          'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[Injected Script] 响应状态:', response.status, response.statusText)

      const responseText = await response.text()
      console.info('[Injected Script] 响应内容:', responseText)

      // 收集响应头
      const headers: Record<string, string> = {}
      for (const [key, value] of response.headers.entries()) {
        headers[key] = value
      }

      let responseData: any = responseText
      try {
        responseData = JSON.parse(responseText)
      } catch (e) {
        // 如果不是JSON，保持原文本
      }

      return {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error) {
      console.error('[Injected Script] 上传失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }
}

// Amazon数据提取和组装一体化函数
async function extractAndAssembleAmazonData(productUrl: string, config?: any): Promise<{ success: boolean; spuData?: any; skuDataList?: any[]; dianxiaomiData?: any; error?: string }> {
  try {
    console.info('[Background] 开始处理Amazon产品:', productUrl)

    // 第一步：获取页面HTML
    const pageHtml = await fetchAmazonPageHtml(productUrl)
    if (!pageHtml) {
      throw new Error('无法获取Amazon页面HTML')
    }

    // 第二步：提取SPU数据
    const spuData = extractSpuDataFromHtml(pageHtml, productUrl)
    if (!spuData.asin || spuData.asin === 'UNKNOWN') {
      throw new Error('无法提取产品ASIN，可能页面结构不正确')
    }

    // 第三步：提取变体数据
    console.info('[Background] 正在提取变体数据...')
    const variationData = extractVariationDataFromHtml(pageHtml)

    // 第四步：判断是否为多变体产品并提取SKU数据
    let skuDataList: any[] = []

    if (isMultiVariantProduct(variationData)) {
      console.info(`[Background] 检测到多变体产品，父ASIN: ${spuData.asin}`)

      // 从变体数据中提取所有SKU的ASIN
      const skuAsins = extractSkuAsinsFromVariations(variationData)
      console.info(`[Background] 找到 ${skuAsins.length} 个SKU变体:`, skuAsins)

      // 并发获取每个SKU的详细信息
      if (skuAsins.length > 0) {
        skuDataList = await fetchMultipleSkuDetails(skuAsins, spuData.asin, variationData)
      }
    } else {
      console.info(`[Background] 检测到单变体产品，ASIN: ${spuData.asin}`)
      skuDataList = createSingleSkuFromHtml(pageHtml, spuData.asin)
    }

    // 第五步：验证数据完整性
    if (skuDataList.length === 0) {
      console.warn('[Background] 未提取到任何SKU数据，创建默认SKU')
      skuDataList = [{
        asin: spuData.asin,
        parentAsin: spuData.asin,
        currency: 'USD',
        stockStatus: 'Unknown',
        price: null,
        imageUrl: spuData.mainImageUrl,
        variationAttributes: JSON.stringify({})
      }]
    }

    console.info(`[Background] 产品数据解析完成: SPU=${spuData.asin}, SKU数量=${skuDataList.length}`)

    // 第六步：组装为店小秘格式
    const assembleResult = await assembleAmazonData(spuData, skuDataList, config)
    if (!assembleResult.success) {
      throw new Error(assembleResult.error || '数据组装失败')
    }

    const dianxiaomiData = assembleResult.data

    console.info('[Background] 数据组装完成')

    return {
      success: true,
      spuData: spuData,
      skuDataList: skuDataList,
      dianxiaomiData: dianxiaomiData
    }
  } catch (error) {
    console.error('[Background] Amazon数据处理失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '处理失败'
    }
  }
}

// 获取Amazon页面HTML
async function fetchAmazonPageHtml(productUrl: string): Promise<string | null> {
  try {
    console.info('[Background] 正在获取Amazon页面HTML:', productUrl)

    const response = await fetch(productUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const html = await response.text()
    console.info('[Background] 页面HTML获取成功，长度:', html.length)
    return html
  } catch (error) {
    console.error('[Background] 获取页面HTML失败:', error)
    return null
  }
}

// ==================== Amazon页面解析函数 ====================

/**
 * 从HTML字符串中提取SPU数据
 * @param {string} html 页面HTML字符串
 * @param {string} pageUrl 页面URL
 * @param {Object} taskData 任务数据
 * @returns {Object} SPU数据对象
 */
function extractSpuDataFromHtml(html: string, pageUrl: string = '', taskData: any = null): any {
  console.info('[Background] 开始提取SPU数据...')

  const spuData: any = {}

  // 1. 提取ASIN（优先级：任务entryAsin > parentAsin > data-asin > URL）
  spuData.asin = extractAsinFromHtml(html, pageUrl, taskData)

  // 2. 提取基础信息
  spuData.title = extractTitleFromHtml(html)
  spuData.brand = extractBrandFromHtml(html)
  spuData.rating = extractRatingFromHtml(html)
  spuData.reviewCount = extractReviewCountFromHtml(html)

  // 3. 提取图片信息
  spuData.mainImageUrl = extractMainImageFromHtml(html)
  spuData.imageUrls = extractImageUrlsFromHtml(html)

  // 4. 提取描述信息
  spuData.bulletPoints = extractBulletPointsFromHtml(html)
  spuData.description = extractDescriptionFromHtml(html)

  // 5. 提取产品详情和规格
  const { productDetails, productAttributes } = extractProductDetailsFromHtml(html)
  spuData.productDetails = JSON.stringify(productDetails)
  spuData.productAttributes = JSON.stringify(productAttributes)

  // 6. 提取类目路径
  spuData.categoryPath = extractCategoryPathFromHtml(html)

  console.info('[Background] SPU数据提取完成:', spuData.asin, '-', spuData.title?.substring(0, 50) + '...')
  return spuData
}

// ==================== 基础提取函数（基于HTML字符串） ====================

/**
 * 提取ASIN
 */
function extractAsinFromHtml(html: string, pageUrl: string, taskData: any): string {
  // 优先使用任务中的entryAsin
  if (taskData && taskData.entryAsin) {
    console.info('[Background] 使用任务entryAsin作为SPU主键:', taskData.entryAsin)
    return taskData.entryAsin
  }

  // 尝试从JavaScript中提取parentAsin
  const parentMatch = html.match(/"parentAsin"\s*:\s*"([^"]+)"/)
  if (parentMatch) {
    console.info('[Background] 使用parentAsin作为SPU主键:', parentMatch[1])
    return parentMatch[1]
  }

  // 从页面元素中提取data-asin
  const dataAsinMatch = html.match(/data-asin="([A-Z0-9]{10})"/)
  if (dataAsinMatch) {
    console.info('[Background] 使用data-asin作为SPU主键:', dataAsinMatch[1])
    return dataAsinMatch[1]
  }

  // 从URL中提取ASIN
  const urlMatch = pageUrl.match(/\/dp\/([A-Z0-9]{10})/)
  const asin = urlMatch ? urlMatch[1] : 'UNKNOWN'
  console.info('[Background] 使用URL中的ASIN作为SPU主键:', asin)
  return asin
}

/**
 * 提取产品标题
 */
function extractTitleFromHtml(html: string): string | null {
  const titleMatch = html.match(/<span[^>]*id="productTitle"[^>]*>(.*?)<\/span>/s)
  return titleMatch ? titleMatch[1].trim().replace(/<[^>]*>/g, '') : null
}

/**
 * 提取品牌信息
 */
function extractBrandFromHtml(html: string): string | null {
  const brandMatch = html.match(/<a[^>]*id="bylineInfo"[^>]*>(.*?)<\/a>/s)
  if (brandMatch) {
    const brandText = brandMatch[1].replace(/<[^>]*>/g, '').trim()
    const storeMatch = brandText.match(/Visit the (.+?) Store/)
    return storeMatch ? storeMatch[1] : brandText
  }
  return null
}

/**
 * 提取评分
 */
function extractRatingFromHtml(html: string): number | null {
  const ratingMatch = html.match(/<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([^<]*out of[^<]*)<\/span>/)
  if (ratingMatch) {
    const ratingText = ratingMatch[1]
    const numberMatch = ratingText.match(/(\d+\.?\d*)/)
    return numberMatch ? parseFloat(numberMatch[1]) : null
  }
  return null
}

/**
 * 提取评价数量
 */
function extractReviewCountFromHtml(html: string): number | null {
  const reviewMatch = html.match(/<span[^>]*id="acrCustomerReviewText"[^>]*>([^<]*)<\/span>/)
  if (reviewMatch) {
    const reviewText = reviewMatch[1].replace(/,/g, '')
    const numberMatch = reviewText.match(/(\d+)/)
    return numberMatch ? parseInt(numberMatch[1]) : null
  }
  return null
}

/**
 * 提取主图URL
 */
function extractMainImageFromHtml(html: string): string | null {
  // 尝试多种模式匹配主图
  const patterns = [
    /<img[^>]*id="landingImage"[^>]*src="([^"]*)"[^>]*>/,
    /<img[^>]*id="imgBlkFront"[^>]*src="([^"]*)"[^>]*>/,
    /<img[^>]*data-old-hires="([^"]*)"[^>]*>/
  ]

  for (const pattern of patterns) {
    const match = html.match(pattern)
    if (match) {
      return match[1]
    }
  }
  return null
}

/**
 * 提取所有图片URLs
 */
function extractImageUrlsFromHtml(html: string): string {
  const imageUrls: string[] = []
  const imagePattern = /<img[^>]*src="([^"]*amazon[^"]*)"[^>]*>/g
  let match

  while ((match = imagePattern.exec(html)) !== null) {
    const imgUrl = match[1]
    if (imgUrl && !imageUrls.includes(imgUrl)) {
      imageUrls.push(imgUrl)
    }
  }

  return JSON.stringify(imageUrls.slice(0, 10)) // 最多10张图片
}

/**
 * 提取五点描述
 */
function extractBulletPointsFromHtml(html: string): string {
  const bulletPoints: string[] = []
  const bulletPattern = /<span[^>]*class="[^"]*a-list-item[^"]*"[^>]*>(.*?)<\/span>/gs
  let match

  while ((match = bulletPattern.exec(html)) !== null) {
    const text = match[1].replace(/<[^>]*>/g, '').trim()
    if (text && text.length > 10 && !text.includes('Make sure')) {
      bulletPoints.push(text)
    }
  }

  return JSON.stringify(bulletPoints.slice(0, 5)) // 最多5个要点
}

/**
 * 提取商品描述
 */
function extractDescriptionFromHtml(html: string): string | null {
  const descPatterns = [
    /<div[^>]*id="productDescription"[^>]*>(.*?)<\/div>/s,
    /<div[^>]*id="aplus"[^>]*>(.*?)<\/div>/s
  ]

  for (const pattern of descPatterns) {
    const match = html.match(pattern)
    if (match) {
      const desc = match[1].replace(/<[^>]*>/g, '').trim()
      return desc.substring(0, 2000) // 最多2000字符
    }
  }
  return null
}

/**
 * 提取产品详情和技术规格
 */
function extractProductDetailsFromHtml(html: string): { productDetails: Record<string, string>, productAttributes: Record<string, string> } {
  const productDetails: Record<string, string> = {}
  const productAttributes: Record<string, string> = {}

  // 方法1: 提取表格中的产品详情 (prodDetTable)
  const tablePattern = /<table[^>]*class="[^"]*prodDetTable[^"]*"[^>]*>(.*?)<\/table>/gs
  let tableMatch

  while ((tableMatch = tablePattern.exec(html)) !== null) {
    const tableContent = tableMatch[1]
    const rowPattern = /<tr[^>]*>(.*?)<\/tr>/gs
    let rowMatch

    while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
      const rowContent = rowMatch[1]
      const cellPattern = /<td[^>]*>(.*?)<\/td>/gs
      const cells: string[] = []
      let cellMatch

      while ((cellMatch = cellPattern.exec(rowContent)) !== null) {
        cells.push(cellMatch[1].replace(/<[^>]*>/g, '').trim())
      }

      if (cells.length >= 2) {
        const key = cells[0].replace(':', '').trim()
        const value = cells[1].trim()
        if (key && value && key !== 'Customer Reviews') {
          productDetails[key] = value
          productAttributes[key] = value
        }
      }
    }
  }

  // 方法2: 提取产品详情区域的其他表格
  const detailTablePatterns = [
    /<table[^>]*id="[^"]*productDetails[^"]*"[^>]*>(.*?)<\/table>/gs,
    /<table[^>]*class="[^"]*a-normal[^"]*"[^>]*>(.*?)<\/table>/gs,
    /<div[^>]*id="productDetails_detailBullets_sections1"[^>]*>(.*?)<\/div>/gs
  ]

  for (const pattern of detailTablePatterns) {
    let match
    while ((match = pattern.exec(html)) !== null) {
      extractAttributesFromTableContent(match[1], productDetails, productAttributes)
    }
  }

  // 方法3: 提取特征列表 (Feature bullets)
  const featurePattern = /<div[^>]*id="feature-bullets"[^>]*>(.*?)<\/div>/gs
  const featureMatch = featurePattern.exec(html)
  if (featureMatch) {
    const featureContent = featureMatch[1]
    const bulletPattern = /<span[^>]*class="[^"]*a-list-item[^"]*"[^>]*>(.*?)<\/span>/gs
    let bulletMatch
    let bulletIndex = 1

    while ((bulletMatch = bulletPattern.exec(featureContent)) !== null) {
      const bulletText = bulletMatch[1].replace(/<[^>]*>/g, '').trim()
      if (bulletText && bulletText.length > 10 && !bulletText.includes('Make sure')) {
        productDetails[`Feature ${bulletIndex}`] = bulletText
        productAttributes[`Feature ${bulletIndex}`] = bulletText
        bulletIndex++
      }
    }
  }

  // 方法4: 从JavaScript数据中提取
  const scriptPattern = /<script[^>]*>([\s\S]*?)<\/script>/gi
  let scriptMatch

  while ((scriptMatch = scriptPattern.exec(html)) !== null) {
    const scriptContent = scriptMatch[1]

    // 提取产品特征数据
    const featureDataMatch = extractJsonFromScript(scriptContent, 'featureData')
    if (featureDataMatch) {
      Object.assign(productAttributes, featureDataMatch)
    }

    // 提取产品规格数据
    const specDataMatch = extractJsonFromScript(scriptContent, 'specifications')
    if (specDataMatch) {
      Object.assign(productAttributes, specDataMatch)
    }
  }

  console.info('[Background] 提取到产品属性数量:', Object.keys(productAttributes).length)
  return { productDetails, productAttributes }
}

/**
 * 从表格内容中提取属性
 */
function extractAttributesFromTableContent(tableContent: string, productDetails: Record<string, string>, productAttributes: Record<string, string>) {
  const rowPattern = /<tr[^>]*>(.*?)<\/tr>/gs
  let rowMatch

  while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
    const rowContent = rowMatch[1]

    // 尝试多种单元格匹配模式
    const cellPatterns = [
      /<td[^>]*>(.*?)<\/td>/gs,
      /<th[^>]*>(.*?)<\/th>/gs,
      /<span[^>]*class="[^"]*a-text-bold[^"]*"[^>]*>(.*?)<\/span>[\s\S]*?<span[^>]*>(.*?)<\/span>/gs
    ]

    for (const cellPattern of cellPatterns) {
      const cells: string[] = []
      let cellMatch

      while ((cellMatch = cellPattern.exec(rowContent)) !== null) {
        if (cellMatch.length === 2) {
          // 标准的td/th匹配
          cells.push(cellMatch[1].replace(/<[^>]*>/g, '').trim())
        } else if (cellMatch.length === 3) {
          // span匹配，第一个是key，第二个是value
          cells.push(cellMatch[1].replace(/<[^>]*>/g, '').trim())
          cells.push(cellMatch[2].replace(/<[^>]*>/g, '').trim())
        }
      }

      if (cells.length >= 2) {
        const key = cells[0].replace(':', '').trim()
        const value = cells[1].trim()
        if (key && value && key !== 'Customer Reviews' && !key.includes('See more')) {
          productDetails[key] = value
          productAttributes[key] = value
        }
        break // 找到匹配后跳出循环
      }
    }
  }
}

/**
 * 提取类目路径
 */
function extractCategoryPathFromHtml(html: string): string {
  const categoryPath: string[] = []
  const breadcrumbPattern = /<a[^>]*href="[^"]*"[^>]*>([^<]*)<\/a>/g
  let match

  // 查找面包屑导航区域
  const breadcrumbSection = html.match(/<div[^>]*id="wayfinding-breadcrumbs_feature_div"[^>]*>(.*?)<\/div>/s)
  if (breadcrumbSection) {
    const sectionContent = breadcrumbSection[1]

    while ((match = breadcrumbPattern.exec(sectionContent)) !== null) {
      const text = match[1].trim()
      if (text && text !== 'Home' && text !== 'Amazon.com') {
        categoryPath.push(text)
      }
    }
  }

  return categoryPath.join(' > ')
}

/**
 * 从HTML字符串中提取变体数据
 * @param {string} html 页面HTML字符串
 * @returns {Object} 变体数据对象
 */
function extractVariationDataFromHtml(html: string): any {
  console.info('[Background] 开始提取变体数据...')

  const variationData: any = {}

  // 使用正则表达式提取script标签内容
  const scriptPattern = /<script[^>]*>([\s\S]*?)<\/script>/gi
  let scriptMatch

  while ((scriptMatch = scriptPattern.exec(html)) !== null) {
    const scriptContent = scriptMatch[1]
    if (!scriptContent || !scriptContent.includes('dimensionToAsinMap')) {
      continue
    }

    try {
      // 提取dimensionToAsinMap - 使用改进的JSON匹配
      const dimensionMatch = extractJsonFromScript(scriptContent, 'dimensionToAsinMap')
      if (dimensionMatch) {
        variationData.dimensionToAsinMap = dimensionMatch
        console.info('[Background] 找到dimensionToAsinMap:', variationData.dimensionToAsinMap)
      }

      // 提取variationValues - 使用改进的JSON匹配
      const variationMatch = extractJsonFromScript(scriptContent, 'variationValues')
      if (variationMatch) {
        variationData.variationValues = variationMatch
        console.info('[Background] 找到variationValues:', variationData.variationValues)
      }

      // 提取colorToAsin - 使用改进的JSON匹配
      const colorToAsinMatch = extractJsonFromScript(scriptContent, 'colorToAsin')
      if (colorToAsinMatch) {
        variationData.colorToAsin = colorToAsinMatch
        console.info('[Background] 找到colorToAsin:', variationData.colorToAsin)
      }

      // 提取colorImages - 使用改进的JSON匹配
      const colorImagesMatch = extractJsonFromScript(scriptContent, 'colorImages')
      if (colorImagesMatch) {
        try {
          variationData.colorImages = colorImagesMatch
          console.info('[Background] 找到colorImages，变体数量:', Object.keys(variationData.colorImages).length)
        } catch (e) {
          console.warn('[Background] 解析colorImages失败:', e)
        }
      }

      // 提取parentAsin
      const parentMatch = scriptContent.match(/"parentAsin"\s*:\s*"([^"]+)"/)
      if (parentMatch) {
        variationData.parentAsin = parentMatch[1]
        console.info('[Background] 找到parentAsin:', parentMatch[1])
      }

      break // 找到数据后退出循环
    } catch (error) {
      console.warn('[Background] 解析变体数据时出错:', error)
      continue
    }
  }

  return variationData
}

/**
 * 从脚本内容中提取指定键的JSON值
 * @param {string} scriptContent 脚本内容
 * @param {string} key 要提取的键名
 * @returns {any} 解析后的JSON对象，失败返回null
 */
function extractJsonFromScript(scriptContent: string, key: string): any {
  try {
    // 查找键的位置
    const keyPattern = new RegExp(`"${key}"\\s*:\\s*`, 'g')
    const keyMatch = keyPattern.exec(scriptContent)

    if (!keyMatch) {
      return null
    }

    // 从键的位置开始查找JSON对象
    let startIndex = keyMatch.index + keyMatch[0].length
    let braceCount = 0
    let inString = false
    let escapeNext = false
    let jsonStart = -1
    let jsonEnd = -1

    for (let i = startIndex; i < scriptContent.length; i++) {
      const char = scriptContent[i]

      if (escapeNext) {
        escapeNext = false
        continue
      }

      if (char === '\\') {
        escapeNext = true
        continue
      }

      if (char === '"' && !escapeNext) {
        inString = !inString
        continue
      }

      if (inString) {
        continue
      }

      if (char === '{') {
        if (jsonStart === -1) {
          jsonStart = i
        }
        braceCount++
      } else if (char === '}') {
        braceCount--
        if (braceCount === 0 && jsonStart !== -1) {
          jsonEnd = i
          break
        }
      }
    }

    if (jsonStart !== -1 && jsonEnd !== -1) {
      const jsonStr = scriptContent.substring(jsonStart, jsonEnd + 1)
      return JSON.parse(jsonStr)
    }

    return null
  } catch (error) {
    console.warn(`[Background] 提取${key}的JSON失败:`, error)
    return null
  }
}

/**
 * 判断是否为多变体产品
 * @param {Object} variationData 变体数据
 * @returns {boolean} 是否为多变体
 */
function isMultiVariantProduct(variationData: any): boolean {
  const hasMultipleDimensions = variationData.dimensionToAsinMap &&
                                Object.keys(variationData.dimensionToAsinMap).length > 1
  const hasMultipleColors = variationData.colorToAsin &&
                           Object.keys(variationData.colorToAsin).length > 1

  return hasMultipleDimensions || hasMultipleColors
}

/**
 * 从变体数据中提取所有SKU的ASIN
 * @param {Object} variationData 变体数据
 * @returns {Array} SKU ASIN数组
 */
function extractSkuAsinsFromVariations(variationData: any): string[] {
  const skuAsins: string[] = []

  if (variationData.dimensionToAsinMap) {
    for (const asin of Object.values(variationData.dimensionToAsinMap)) {
      if (asin && !skuAsins.includes(asin as string)) {
        skuAsins.push(asin as string)
      }
    }
  }

  if (variationData.colorToAsin) {
    for (const asin of Object.values(variationData.colorToAsin)) {
      if (asin && !skuAsins.includes(asin as string)) {
        skuAsins.push(asin as string)
      }
    }
  }

  return skuAsins
}

/**
 * 并发获取多个SKU的详细信息
 * @param {Array} skuAsins SKU ASIN数组
 * @param {string} parentAsin 父ASIN
 * @param {Object} variationData 变体数据
 * @returns {Promise<Array>} SKU数据数组
 */
async function fetchMultipleSkuDetails(skuAsins: string[], parentAsin: string, variationData: any): Promise<any[]> {
  console.info(`[Background] 开始并发获取 ${skuAsins.length} 个SKU的详细信息...`)

  const skuDataList: any[] = []
  const maxConcurrent = 3 // 最大并发数

  // 分批处理，避免过多并发请求
  for (let i = 0; i < skuAsins.length; i += maxConcurrent) {
    const batch = skuAsins.slice(i, i + maxConcurrent)
    const batchPromises = batch.map(async (asin) => {
      try {
        const skuUrl = `https://www.amazon.com/dp/${asin}`
        const skuHtml = await fetchAmazonPageHtml(skuUrl)

        if (skuHtml) {
          return createSkuDataFromHtml(skuHtml, asin, parentAsin, variationData)
        }
        return null
      } catch (error) {
        console.warn(`[Background] 获取SKU ${asin} 详情失败:`, error)
        return null
      }
    })

    const batchResults = await Promise.all(batchPromises)

    // 添加成功获取的SKU数据
    for (const skuData of batchResults) {
      if (skuData) {
        skuDataList.push(skuData)
      }
    }

    // 批次间等待，避免请求过快
    if (i + maxConcurrent < skuAsins.length) {
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
  }

  console.info(`[Background] SKU详情获取完成，成功获取 ${skuDataList.length}/${skuAsins.length} 个SKU`)
  return skuDataList
}

/**
 * 从HTML字符串创建SKU数据
 * @param {string} html 页面HTML字符串
 * @param {string} asin SKU ASIN
 * @param {string} parentAsin 父ASIN
 * @param {Object} variationData 变体数据
 * @returns {Object} SKU数据对象
 */
function createSkuDataFromHtml(html: string, asin: string, parentAsin: string, variationData: any): any {
  const skuData: any = {
    asin: asin,
    parentAsin: parentAsin,
    currency: 'USD',
    stockStatus: 'In Stock',
    price: null,
    imageUrl: null,
    variationAttributes: {}
  }

  // 提取价格
  skuData.price = extractPriceFromHtml(html)

  // 提取主图
  skuData.imageUrl = extractMainImageFromHtml(html)

  // 提取库存状态
  skuData.stockStatus = extractStockStatusFromHtml(html)

  // 解析变体属性
  const variationAttributes = parseVariationAttributesForSku(asin, variationData)
  skuData.variationAttributes = JSON.stringify(variationAttributes)

  return skuData
}

/**
 * 为单变体产品创建SKU数据
 * @param {string} html 页面HTML字符串
 * @param {string} parentAsin 父ASIN
 * @returns {Array} 包含单个SKU的数组
 */
function createSingleSkuFromHtml(html: string, parentAsin: string): any[] {
  console.info('[Background] 创建单变体SKU数据...')

  const skuData: any = {
    asin: parentAsin,
    parentAsin: parentAsin,
    currency: 'USD',
    stockStatus: 'In Stock',
    price: null,
    imageUrl: null,
    variationAttributes: JSON.stringify({})
  }

  // 提取价格
  skuData.price = extractPriceFromHtml(html)

  // 提取主图
  skuData.imageUrl = extractMainImageFromHtml(html)

  // 提取库存状态
  skuData.stockStatus = extractStockStatusFromHtml(html)

  console.info('[Background] 单变体SKU创建完成')
  return [skuData]
}

/**
 * 提取价格
 */
function extractPriceFromHtml(html: string): number | null {
  const pricePatterns = [
    /<span[^>]*class="[^"]*a-price[^"]*a-offscreen[^"]*"[^>]*>([^<]*)<\/span>/,
    /<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$([^<]*)<\/span>/
  ]

  for (const pattern of pricePatterns) {
    const match = html.match(pattern)
    if (match) {
      const priceText = match[1].replace('$', '').replace(',', '')
      const price = parseFloat(priceText)
      if (!isNaN(price)) {
        return price
      }
    }
  }
  return null
}

/**
 * 提取库存状态
 */
function extractStockStatusFromHtml(html: string): string {
  const availabilityPatterns = [
    /<div[^>]*id="availability"[^>]*>(.*?)<\/div>/s,
    /<div[^>]*id="availabilityInsideBuyBox_feature_div"[^>]*>(.*?)<\/div>/s
  ]

  for (const pattern of availabilityPatterns) {
    const match = html.match(pattern)
    if (match) {
      const text = match[1].replace(/<[^>]*>/g, '').trim().toLowerCase()
      if (text.includes('in stock')) return 'In Stock'
      if (text.includes('out of stock')) return 'Out of Stock'
      if (text.includes('temporarily unavailable')) return 'Temporarily Unavailable'
    }
  }

  return 'Unknown'
}

/**
 * 解析SKU的变体属性
 */
function parseVariationAttributesForSku(asin: string, variationData: any): Record<string, string> {
  const attributes: Record<string, string> = {}

  // 从colorToAsin中查找颜色属性
  if (variationData.colorToAsin) {
    for (const [color, colorAsin] of Object.entries(variationData.colorToAsin)) {
      if (colorAsin === asin) {
        attributes.Color = color
        break
      }
    }
  }

  // 从dimensionToAsinMap中查找其他属性
  if (variationData.dimensionToAsinMap && variationData.variationValues) {
    const sizeNames = variationData.variationValues.size_name || []

    for (const [index, dimensionAsin] of Object.entries(variationData.dimensionToAsinMap)) {
      if (dimensionAsin === asin) {
        const indexNum = parseInt(index)
        if (sizeNames[indexNum]) {
          attributes.Size = sizeNames[indexNum]
        }
        break
      }
    }
  }

  return attributes
}



// Amazon数据组装函数
async function assembleAmazonData(spuData: any, skuDataList: any[], config?: any): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.info('[Background] 开始组装Amazon数据为店小秘格式...')

    // 默认配置
    const defaultConfig = {
      basic: {
        categoryId: "9938",
        shopId: "6959965",
        businessSite: "43000000000006",
        warehouse: "WH-06912611061892972",
        freightTemplate: "HFT-14821249525104782972",
        shippingTime: "172800",
        publishStatus: "2"
      },
      product: {
        titlePrefix: "",
        titleSuffix: "",
        priceMultiplier: 4,
        defaultSize: {
          length: 127,
          width: 23,
          height: 19,
          weight: 2000
        },
        fixedStock: 200
      }
    }

    // 合并配置
    const finalConfig = config || defaultConfig

    // 生成唯一ID
    const uniqueId = Date.now().toString()

    // 构建属性数据
    const buildAttributes = (specifications: Record<string, string>): string => {
      const attributes = Object.entries(specifications).map(([key, value], index) => ({
        propName: key,
        refPid: 4000 + index,
        pid: 1700 + index,
        templatePid: 1200000 + index,
        numberInputValue: "",
        valueUnit: "",
        vid: (67000 + index).toString(),
        propValue: value
      }))
      return JSON.stringify(attributes)
    }

    // 构建商品名称
    const buildProductName = (title: string, prefix: string, suffix: string): string => {
      let productName = title
      if (prefix) productName = prefix + ' ' + productName
      if (suffix) productName = productName + ' ' + suffix
      return productName.substring(0, 200)
    }

    // 构建变体数据
    const buildVariationListStr = (): string => {
      const variations = skuDataList.map((sku, index) => ({
        id: uniqueId + '_' + index,
        productSkuId: 0,
        supplierPrice: Math.round((sku.price || 10) * finalConfig.product.priceMultiplier * 100),
        extCode: `${sku.asin}[am]${index + 1}`,
        length: finalConfig.product.defaultSize.length,
        width: finalConfig.product.defaultSize.width,
        height: finalConfig.product.defaultSize.height,
        weight: finalConfig.product.defaultSize.weight,
        codeType: "1",
        code: "",
        suggestedPrice: Math.round((sku.price || 10) * finalConfig.product.priceMultiplier * 100),
        suggestedPriceCurrencyType: "CNY",
        numberOfPieces: 1,
        skuClassification: "1",
        pieceUnitCode: "1",
        individuallyPacked: null,
        thumbUrl: sku.imageUrl || spuData.mainImageUrl || "",
        productSkuSpecReqs: sku.variationAttributes || JSON.stringify([{
          specId: "0",
          specName: "Default",
          parentSpecId: 1001,
          parentSpecName: "颜色"
        }]),
        productSkuStockQuantityReq: JSON.stringify([{
          warehouseId: finalConfig.basic.warehouse,
          targetStockAvailable: finalConfig.product.fixedStock?.toString() || "200"
        }]),
        sitePriceInfo: null
      }))
      return JSON.stringify(variations)
    }

    // 构建描述数据
    const buildDescription = (): string => {
      const description = []

      // 添加文字描述
      const bulletPoints = JSON.parse(spuData.bulletPoints || '[]')
      if (bulletPoints && bulletPoints.length > 0) {
        description.push({
          lang: "zh",
          type: "text",
          priority: "0",
          contentList: [{
            text: bulletPoints.join(' '),
            textModuleDetails: {
              fontFamily: null,
              fontColor: "#000000",
              backgroundColor: "#ffffff",
              fontSize: "12",
              align: "left"
            }
          }]
        })
      }

      // 添加图片
      const imageUrls = JSON.parse(spuData.imageUrls || '[]')
      if (imageUrls && imageUrls.length > 0) {
        imageUrls.forEach((imageUrl: string, index: number) => {
          description.push({
            lang: "zh",
            type: "image",
            priority: (index + 1).toString(),
            contentList: [{
              imgUrl: imageUrl,
              height: 800,
              width: 800
            }]
          })
        })
      }

      return JSON.stringify(description)
    }

    // 组装店小秘格式数据
    const productAttributes = JSON.parse(spuData.productAttributes || '{}')
    const imageUrls = JSON.parse(spuData.imageUrls || '[]')

    const dianxiaomiData = {
      attributes: buildAttributes(productAttributes),
      categoryId: finalConfig.basic.categoryId,
      shopId: finalConfig.basic.shopId,
      productSemiManagedReq: "100",
      sourceUrl: spuData.sourceUrl || "",
      fullCid: "4547939-",
      productName: buildProductName(spuData.title || "", finalConfig.product.titlePrefix, finalConfig.product.titleSuffix),
      productNameI18n: JSON.stringify({ en: spuData.title || "" }),
      outerGoodsUrl: spuData.sourceUrl || "",
      materialImgUrl: spuData.mainImageUrl || "",
      productOrigin: "CN",
      region2Id: finalConfig.basic.businessSite,
      originFileUrl: "",
      sensitiveAttr: "",
      personalizationSwitch: 0,
      mainImage: imageUrls.join('|'),
      dxmVideoId: "0",
      optionValue: "[]",
      mainProductSkuSpecReqs: JSON.stringify([{
        parentSpecId: 0,
        parentSpecName: "",
        specId: 0,
        specName: "",
        previewImgUrls: spuData.mainImageUrl || "",
        extCode: `${spuData.asin}[am]1`,
        productSkcId: ""
      }]),
      goodsModel: "",
      variationListStr: buildVariationListStr(),
      productWarehouseRouteReq: JSON.stringify([{
        warehouseId: finalConfig.basic.warehouse,
        warehouseName: "Amazon仓库",
        siteIdList: [finalConfig.basic.businessSite || "100"]
      }]),
      dxmPdfUrl: "",
      qualifiedEn: "",
      instructionsId: "",
      instructionsName: "",
      description: buildDescription(),
      instructionsTranslateId: "",
      freightTemplateId: finalConfig.basic.freightTemplate,
      shipmentLimitSecond: parseInt(finalConfig.basic.shippingTime) || 172800,
      op: 1,
      id: uniqueId,
      categoryType: 0,
      dxmState: finalConfig.basic.publishStatus === "2" ? "online" : "offline",
      productId: "0",
      sizeTemplateIds: ""
    }

    console.info('[Background] Amazon数据组装完成:', dianxiaomiData)

    return {
      success: true,
      data: dianxiaomiData
    }
  } catch (error) {
    console.error('[Background] Amazon数据组装失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '数据组装失败'
    }
  }
}

// 创建服务实例
const temuService = new TemuBackgroundService()
const dianxiaomiService = new DianxiaomiBackgroundService()
const amazonService = new AmazonBackgroundService()

// 监听来自 side panel 的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.info('[Background] 收到消息:', request)

  if (request.action === 'GET_TEMU_SHOP_INFO') {
    // 异步获取 Temu 店铺信息
    temuService.getTemuShopInfo()
      .then(result => {
        console.info('[Background] 获取结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    // 返回 true 表示异步响应
    return true
  }

  if (request.action === 'SAVE_PRODUCT_CONFIG') {
    // 处理商品配置保存
    console.info('[Background] 收到商品配置保存请求:', request.config)

    try {
      // 这里可以添加额外的处理逻辑，比如验证配置数据
      const config = request.config

      if (config && config.form && config.form.shopId && config.form.categoryId) {
        // 配置数据有效，可以进行保存
        console.info('[Background] 商品配置数据有效，准备保存:', {
          shopId: config.form.shopId,
          categoryId: config.form.categoryId,
          categoryName: config.form.categoryName
        })

        sendResponse({
          success: true,
          message: '商品配置已接收并处理',
          data: {
            shopId: config.form.shopId,
            categoryId: config.form.categoryId,
            timestamp: new Date().toISOString()
          }
        })
      } else {
        console.warn('[Background] 商品配置数据格式不正确:', config)
        sendResponse({
          success: false,
          error: '配置数据格式不正确'
        })
      }
    } catch (error) {
      console.error('[Background] 处理商品配置保存失败:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : '处理失败'
      })
    }

    return true
  }

  // 处理店小秘API调用请求
  if (request.action === 'CALL_DIANXIAOMI_API') {
    console.info('[Background] 收到店小秘API调用请求:', request.apiConfig)

    dianxiaomiService.callDianxiaomiAPI(request.apiConfig)
      .then(result => {
        console.info('[Background] 店小秘API调用结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 店小秘API调用失败:', error)
        sendResponse({
          success: false,
          error: error.message || '调用失败'
        })
      })

    return true
  }

  // 处理获取店小秘Token状态请求
  if (request.action === 'GET_DIANXIAOMI_TOKEN_STATUS') {
    console.info('[Background] 收到获取店小秘Token状态请求')

    dianxiaomiService.getDianxiaomiTokenStatus()
      .then(result => {
        console.info('[Background] 获取Token状态结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取Token状态失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理获取店铺列表请求
  if (request.action === 'GET_DIANXIAOMI_SHOP_LIST') {
    console.info('[Background] 收到获取店铺列表请求')

    dianxiaomiService.getShopList()
      .then(result => {
        console.info('[Background] 获取店铺列表结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取店铺列表失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理获取运费模板请求
  if (request.action === 'GET_FREIGHT_TEMPLATES') {
    console.info('[Background] 收到获取运费模板请求:', request.shopId)

    dianxiaomiService.getFreightTemplates(request.shopId)
      .then(result => {
        console.info('[Background] 获取运费模板结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取运费模板失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理获取商品分类请求
  if (request.action === 'GET_PRODUCT_CATEGORIES') {
    console.info('[Background] 收到获取商品分类请求:', request.shopId)

    dianxiaomiService.getProductCategories(request.shopId)
      .then(result => {
        console.info('[Background] 获取商品分类结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 获取商品分类失败:', error)
        sendResponse({
          success: false,
          error: error.message || '获取失败'
        })
      })

    return true
  }

  // 处理商品上传请求
  if (request.action === 'UPLOAD_PRODUCT') {
    console.info('[Background] 收到商品上传请求:', request.productData)

    dianxiaomiService.uploadProduct(request.productData)
      .then(result => {
        console.info('[Background] 商品上传结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 商品上传失败:', error)
        sendResponse({
          success: false,
          error: error.message || '上传失败'
        })
      })

    return true
  }

  // 处理创建ZIP文件请求 - 现在可以在background中使用JSZip了
  if (request.action === 'CREATE_ZIP_FILE') {
    console.info('[Background] 收到创建ZIP文件请求')

    dianxiaomiService.createZipFile(request.jsonData)
      .then(result => {
        console.info('[Background] ZIP文件创建结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] ZIP文件创建失败:', error)
        sendResponse({
          success: false,
          error: error.message || '创建失败'
        })
      })

    return true
  }

  // 处理店小秘商品上传请求 - 借鉴test_upload.html逻辑
  if (request.action === 'UPLOAD_DIANXIAOMI_PRODUCT') {
    console.info('[Background] 收到店小秘商品上传请求')

    dianxiaomiService.uploadDianxiaomiProduct(request.jsonData)
      .then(result => {
        console.info('[Background] 店小秘商品上传结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] 店小秘商品上传失败:', error)
        sendResponse({
          success: false,
          error: error.message || '上传失败'
        })
      })

    return true
  }

  // 处理Amazon数据提取和组装请求（一体化）
  if (request.action === 'EXTRACT_AND_ASSEMBLE_AMAZON_DATA') {
    console.info('[Background] 收到Amazon数据提取和组装请求:', request.productUrl)

    extractAndAssembleAmazonData(request.productUrl)
      .then(result => {
        console.info('[Background] Amazon数据处理结果:', result)
        sendResponse(result)
      })
      .catch(error => {
        console.error('[Background] Amazon数据处理失败:', error)
        sendResponse({
          success: false,
          error: error.message || '处理失败'
        })
      })

    return true
  }

  // 处理JSZip可用性测试请求
  if (request.action === 'TEST_JSZIP_AVAILABILITY') {
    console.info('[Background] 收到JSZip可用性测试请求')

    try {
      // 检查JSZip是否可用
      if (typeof JSZip === 'undefined') {
        sendResponse({
          success: false,
          error: 'JSZip库不可用，请检查导入配置'
        })
      } else {
        // 测试JSZip基本功能
        const zip = new JSZip()
        zip.file('test.txt', 'JSZip test successful!')

        sendResponse({
          success: true,
          version: JSZip.version || '未知版本',
          message: 'JSZip库在background中可用'
        })

        console.info('[Background] JSZip可用性测试成功')
      }
    } catch (error) {
      console.error('[Background] JSZip可用性测试失败:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'JSZip测试失败'
      })
    }

    return true
  }

  return false
})

export {}
