// 临时手动拦截器 - 直接复制 ultimate-debug.js 的成功逻辑
// 在Temu商家后台页面控制台运行，作为扩展拦截器的备用方案

console.log('🔧 启动临时手动拦截器...');

window.temporaryInterceptor = {
    antiContentFound: null,
    mallIdFound: null,
    interceptedRequests: [],
    
    // 设置拦截器
    setup: function() {
        console.log('⚙️ 设置临时拦截器...');
        
        // 1. 拦截fetch请求
        const originalFetch = window.fetch;
        const self = this;
        
        window.fetch = function(...args) {
            const [url, options] = args;
            const urlString = typeof url === 'string' ? url : url.toString();

            if (urlString.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 [临时拦截器] 拦截fetch请求:', urlString);
                console.log('📋 [临时拦截器] 请求选项:', options);

                if (options && options.headers) {
                    self.checkHeaders(options.headers, 'fetch', urlString);
                }
            }

            return originalFetch.apply(this, args);
        };
        
        // 2. 拦截XMLHttpRequest
        const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        
        const xhrInstances = new WeakMap();
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            const urlString = url.toString();
            xhrInstances.set(this, { url: urlString, headers: {} });

            if (urlString.includes('seller.kuajingmaihuo.com')) {
                console.log('🌐 [临时拦截器] XHR open:', method, urlString);
            }

            return originalOpen.call(this, method, url, ...args);
        };

        XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
            const instance = xhrInstances.get(this);
            if (instance) {
                instance.headers[name] = value;

                if (instance.url.includes('seller.kuajingmaihuo.com')) {
                    console.log('🎯 [临时拦截器] XHR设置头部:', name, '=', value.substring(0, 100) + (value.length > 100 ? '...' : ''));
                    self.checkSingleHeader(name, value, 'XHR', instance.url);
                }
            }

            return originalSetRequestHeader.call(this, name, value);
        };

        XMLHttpRequest.prototype.send = function(body) {
            const instance = xhrInstances.get(this);
            if (instance && instance.url.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 [临时拦截器] XHR发送请求:', instance.url);
                console.log('📋 [临时拦截器] 所有头部:', instance.headers);
                self.checkHeaders(instance.headers, 'XHR', instance.url);
            }

            return originalSend.call(this, body);
        };
        
        console.log('✅ 临时拦截器设置完成');
    },
    
    // 检查请求头
    checkHeaders: function(headers, source, url) {
        console.log(`📋 [临时拦截器] 检查${source}请求头:`, headers);

        // 检查anti-content的所有可能变体
        const antiContentKeys = [
            'anti-content', 'Anti-Content', 'ANTI-CONTENT', 'antiContent',
            'anti_content', 'Anti_Content', 'ANTI_CONTENT'
        ];

        let foundAntiContent = null;
        let foundKey = null;

        for (const key of antiContentKeys) {
            if (headers[key]) {
                foundAntiContent = headers[key];
                foundKey = key;
                break;
            }
        }

        if (foundAntiContent) {
            this.antiContentFound = foundAntiContent;
            console.log(`🎉 [临时拦截器] 找到 anti-content (${source}, key: ${foundKey}):`, foundAntiContent);

            // 保存到localStorage
            try {
                localStorage.setItem('temporary_anti_content', foundAntiContent);
                localStorage.setItem('temporary_anti_content_source', source);
                localStorage.setItem('temporary_anti_content_key', foundKey);
                localStorage.setItem('temporary_anti_content_time', Date.now().toString());
                localStorage.setItem('temporary_anti_content_url', url);

                // 兼容扩展的键名
                localStorage.setItem('temu_cached_anti_content', foundAntiContent);
                localStorage.setItem('temu_anti_content_expiry', (Date.now() + 30 * 60 * 1000).toString());

                console.log('✅ [临时拦截器] 成功保存 anti-content 到localStorage');
                
                // 显示成功提示
                if (typeof alert !== 'undefined') {
                    alert('🎉 临时拦截器成功获取到 anti-content！扩展现在应该能正常工作了。');
                }
            } catch (e) {
                console.warn('[临时拦截器] 保存anti-content失败:', e);
            }
        }

        // 检查mallid的所有可能变体
        const mallIdKeys = ['mallid', 'mallId', 'MallId', 'MALLID', 'mall_id', 'Mall_Id'];

        let foundMallId = null;
        let foundMallKey = null;

        for (const key of mallIdKeys) {
            if (headers[key]) {
                foundMallId = headers[key];
                foundMallKey = key;
                break;
            }
        }

        if (foundMallId) {
            this.mallIdFound = foundMallId;
            console.log(`🎉 [临时拦截器] 找到 mallId (${source}, key: ${foundMallKey}):`, foundMallId);

            // 保存到localStorage
            try {
                localStorage.setItem('temporary_mall_id', foundMallId);
                localStorage.setItem('temporary_mall_id_source', source);
                localStorage.setItem('temporary_mall_id_key', foundMallKey);
                localStorage.setItem('temporary_mall_id_time', Date.now().toString());

                // 兼容扩展的键名
                localStorage.setItem('temu_cached_mall_id', foundMallId);
                localStorage.setItem('temu_mall_id_expiry', (Date.now() + 60 * 60 * 1000).toString());

                console.log('✅ [临时拦截器] 成功保存 mallId 到localStorage');
            } catch (e) {
                console.warn('[临时拦截器] 保存mallId失败:', e);
            }
        }

        // 记录请求
        this.interceptedRequests.push({
            source,
            url,
            headers,
            antiContent: foundAntiContent,
            mallId: foundMallId,
            timestamp: Date.now()
        });
    },
    
    // 检查单个请求头
    checkSingleHeader: function(name, value, source, url) {
        const lowerName = name.toLowerCase();

        if (lowerName.includes('anti') && lowerName.includes('content')) {
            this.antiContentFound = value;
            console.log(`🎉 [临时拦截器] 找到 anti-content (${source}, header: ${name}):`, value);

            try {
                localStorage.setItem('temporary_anti_content', value);
                localStorage.setItem('temporary_anti_content_source', source);
                localStorage.setItem('temporary_anti_content_key', name);
                localStorage.setItem('temporary_anti_content_time', Date.now().toString());
                localStorage.setItem('temporary_anti_content_url', url);
                localStorage.setItem('temu_cached_anti_content', value);
                localStorage.setItem('temu_anti_content_expiry', (Date.now() + 30 * 60 * 1000).toString());
                
                console.log('✅ [临时拦截器] 单头部保存 anti-content 成功');
            } catch (e) {
                console.warn('[临时拦截器] 保存anti-content失败:', e);
            }
        }

        if (lowerName.includes('mall') && lowerName.includes('id')) {
            this.mallIdFound = value;
            console.log(`🎉 [临时拦截器] 找到 mallId (${source}, header: ${name}):`, value);

            try {
                localStorage.setItem('temporary_mall_id', value);
                localStorage.setItem('temporary_mall_id_source', source);
                localStorage.setItem('temporary_mall_id_key', name);
                localStorage.setItem('temporary_mall_id_time', Date.now().toString());
                localStorage.setItem('temu_cached_mall_id', value);
                localStorage.setItem('temu_mall_id_expiry', (Date.now() + 60 * 60 * 1000).toString());
                
                console.log('✅ [临时拦截器] 单头部保存 mallId 成功');
            } catch (e) {
                console.warn('[临时拦截器] 保存mallId失败:', e);
            }
        }
    },
    
    // 获取报告
    report: function() {
        console.log('📊 临时拦截器报告:', {
            timestamp: new Date().toLocaleString(),
            interceptedRequests: this.interceptedRequests.length,
            antiContentFound: this.antiContentFound,
            mallIdFound: this.mallIdFound
        });
        
        return {
            antiContentFound: this.antiContentFound,
            mallIdFound: this.mallIdFound,
            interceptedRequests: this.interceptedRequests
        };
    },
    
    // 检查缓存状态
    checkCache: function() {
        const tempAntiContent = localStorage.getItem('temporary_anti_content');
        const cachedAntiContent = localStorage.getItem('temu_cached_anti_content');
        const tempMallId = localStorage.getItem('temporary_mall_id');
        const cachedMallId = localStorage.getItem('temu_cached_mall_id');
        
        console.log('💾 临时拦截器缓存状态:');
        console.log('temporary_anti_content:', tempAntiContent ? tempAntiContent.substring(0, 50) + '...' : 'null');
        console.log('temu_cached_anti_content:', cachedAntiContent ? cachedAntiContent.substring(0, 50) + '...' : 'null');
        console.log('temporary_mall_id:', tempMallId);
        console.log('temu_cached_mall_id:', cachedMallId);
        
        return {
            tempAntiContent,
            cachedAntiContent,
            tempMallId,
            cachedMallId
        };
    },
    
    // 设置为扩展的全局接口（兼容性）
    setupAsExtensionInterface: function() {
        window.temuEarlyInterceptor = {
            getCachedAntiContent: () => this.antiContentFound || localStorage.getItem('temu_cached_anti_content'),
            getCachedMallId: () => this.mallIdFound || localStorage.getItem('temu_cached_mall_id'),
            getState: () => ({
                antiContentFound: this.antiContentFound || localStorage.getItem('temu_cached_anti_content'),
                mallIdFound: this.mallIdFound || localStorage.getItem('temu_cached_mall_id'),
                interceptedRequests: this.interceptedRequests
            }),
            report: () => this.report(),
            clearCache: () => {
                this.antiContentFound = null;
                this.mallIdFound = null;
                this.interceptedRequests = [];
                localStorage.removeItem('temporary_anti_content');
                localStorage.removeItem('temu_cached_anti_content');
                localStorage.removeItem('temporary_mall_id');
                localStorage.removeItem('temu_cached_mall_id');
                console.log('🗑️ [临时拦截器] 缓存已清除');
            }
        };
        
        console.log('✅ 已设置为扩展兼容接口');
    }
};

// 自动启动
window.temporaryInterceptor.setup();
window.temporaryInterceptor.setupAsExtensionInterface();

console.log('✅ 临时手动拦截器启动完成！');
console.log('💡 运行 temporaryInterceptor.report() 查看状态');
console.log('💡 运行 temporaryInterceptor.checkCache() 检查缓存');
console.log('💡 现在请在页面中进行一些操作，触发包含 anti-content 的请求');
