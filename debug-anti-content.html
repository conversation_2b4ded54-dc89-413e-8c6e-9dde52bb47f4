<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anti-Content 调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .button.danger {
            background: #ff4d4f;
        }
        .button.danger:hover {
            background: #ff7875;
        }
        .result {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Anti-Content 调试工具</h1>
        <p>此工具用于调试 Temu 扩展中的 anti-content 获取问题。</p>
        
        <div>
            <button class="button" onclick="debugAntiContent()">🔍 调试 Anti-Content 获取</button>
            <button class="button" onclick="testTodoCount()">📊 测试待办事项获取</button>
            <button class="button danger" onclick="clearResults()">🗑️ 清空结果</button>
        </div>
        
        <div id="status" class="status warning">等待操作...</div>
    </div>

    <div class="container">
        <h2>📋 调试结果</h2>
        <div id="results"></div>
    </div>

    <div class="container">
        <h2>📖 使用说明</h2>
        <ol>
            <li>确保已安装并启用了 Temu 扩展</li>
            <li>在新标签页中打开 <a href="https://seller.kuajingmaihuo.com" target="_blank">Temu 商家后台</a></li>
            <li>回到此页面，点击"调试 Anti-Content 获取"按钮</li>
            <li>查看控制台和结果区域的详细信息</li>
        </ol>
        
        <h3>🔧 常见问题</h3>
        <ul>
            <li><strong>Chrome API 不可用</strong>：请确保在扩展环境中运行此页面</li>
            <li><strong>未找到 Temu 商家后台标签页</strong>：请先打开 Temu 商家后台</li>
            <li><strong>Anti-content 为空</strong>：可能需要在 Temu 页面中进行一些操作来触发生成</li>
        </ul>
    </div>

    <script>
        let resultCounter = 0;

        function addResult(title, content, type = 'info') {
            resultCounter++;
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `
                <strong>[${new Date().toLocaleTimeString()}] ${title}</strong>
                <br><br>
                ${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}
            `;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function updateStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        async function debugAntiContent() {
            updateStatus('正在调试 anti-content 获取...', 'warning');
            addResult('开始调试', '正在尝试获取 anti-content...');

            try {
                // 检查是否在扩展环境中
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('不在扩展环境中，请在扩展的弹窗或侧边栏中运行此页面');
                }

                // 动态导入 temuDataService
                const { default: temuDataService } = await import('./src/services/temuDataService.ts');
                
                addResult('服务加载', 'TemuDataService 加载成功');

                // 调用调试方法
                const result = await temuDataService.debugAntiContent();
                
                if (result.antiContent) {
                    updateStatus('✅ Anti-content 获取成功！', 'success');
                    addResult('调试成功', {
                        antiContent: result.antiContent.substring(0, 100) + '...',
                        fullLength: result.antiContent.length,
                        debug: result.debug
                    }, 'success');
                } else {
                    updateStatus('⚠️ Anti-content 为空', 'warning');
                    addResult('调试结果', '未能获取到 anti-content，请查看控制台了解详细信息', 'error');
                }

            } catch (error) {
                updateStatus('❌ 调试失败', 'error');
                addResult('调试失败', error.message || error.toString(), 'error');
                console.error('调试失败:', error);
            }
        }

        async function testTodoCount() {
            updateStatus('正在测试待办事项获取...', 'warning');
            addResult('开始测试', '正在尝试获取待办事项数据...');

            try {
                // 检查是否在扩展环境中
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('不在扩展环境中，请在扩展的弹窗或侧边栏中运行此页面');
                }

                // 动态导入 temuDataService
                const { default: temuDataService } = await import('./src/services/temuDataService.ts');
                
                addResult('服务加载', 'TemuDataService 加载成功');

                // 调用待办事项获取方法
                const result = await temuDataService.getTodoCount();
                
                updateStatus('✅ 待办事项获取成功！', 'success');
                addResult('测试成功', result, 'success');

            } catch (error) {
                updateStatus('❌ 测试失败', 'error');
                addResult('测试失败', error.message || error.toString(), 'error');
                console.error('测试失败:', error);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            resultCounter = 0;
            updateStatus('结果已清空', 'warning');
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addResult('页面初始化', '调试工具已准备就绪');
            
            // 检查扩展环境
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                addResult('环境检查', '✅ 扩展环境检测成功');
                updateStatus('✅ 扩展环境正常', 'success');
            } else {
                addResult('环境检查', '❌ 未检测到扩展环境，请在扩展中运行此页面', 'error');
                updateStatus('❌ 扩展环境异常', 'error');
            }
        });
    </script>
</body>
</html>
