// 优化的anti-content测试脚本
// 基于成功的拦截结果，去除无关判断条件

console.log('🎯 启动优化的anti-content测试脚本...');

// 简化的状态管理
const optimizedTest = {
    interceptedRequests: [],
    antiContentFound: null,
    mallIdFound: null
};

// 1. 精准的网络拦截（只拦截必要的）
function setupOptimizedInterception() {
    console.log('🌐 设置优化的网络拦截...');
    
    // 拦截fetch（主要方式）
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const [url, options] = args;
        const urlString = typeof url === 'string' ? url : url.toString();
        
        // 只关注Temu API请求
        if (urlString.includes('seller.kuajingmaihuo.com')) {
            console.log('🎯 拦截Temu API请求:', urlString);
            
            if (options && options.headers) {
                checkOptimizedHeaders(options.headers, 'fetch', urlString);
            }
        }
        
        return originalFetch.apply(this, args);
    };
    
    console.log('✅ 优化网络拦截设置完成');
}

// 2. 精准的头部检查（基于成功的拦截结果）
function checkOptimizedHeaders(headers, source, url) {
    // 基于成功的拦截，我们知道键名是 'anti-content' 和 'mallid'
    if (headers['anti-content']) {
        optimizedTest.antiContentFound = headers['anti-content'];
        console.log(`🎉 找到 anti-content (${source}):`, headers['anti-content'].substring(0, 50) + '...');
        
        // 保存到localStorage
        try {
            localStorage.setItem('optimized_anti_content', headers['anti-content']);
            localStorage.setItem('optimized_anti_content_time', Date.now().toString());
            localStorage.setItem('optimized_anti_content_url', url);
            console.log('✅ anti-content 已保存到localStorage');
        } catch (e) {
            console.warn('保存anti-content失败:', e);
        }
    }
    
    if (headers['mallid']) {
        optimizedTest.mallIdFound = headers['mallid'];
        console.log(`🎉 找到 mallid (${source}):`, headers['mallid']);
        
        // 保存到localStorage
        try {
            localStorage.setItem('optimized_mall_id', headers['mallid']);
            localStorage.setItem('optimized_mall_id_time', Date.now().toString());
            console.log('✅ mallid 已保存到localStorage');
        } catch (e) {
            console.warn('保存mallid失败:', e);
        }
    }
    
    // 记录请求
    optimizedTest.interceptedRequests.push({
        source,
        url,
        antiContent: headers['anti-content'] || null,
        mallId: headers['mallid'] || null,
        timestamp: Date.now()
    });
}

// 3. 检查扩展的早期拦截器
function checkEarlyInterceptor() {
    console.log('🔍 检查早期拦截器状态...');
    
    if (window.temuEarlyInterceptor) {
        const cache = window.temuEarlyInterceptor.getCache();
        console.log('✅ 早期拦截器工作正常');
        console.log('💾 早期拦截器缓存:', {
            antiContent: cache.antiContent ? cache.antiContent.substring(0, 50) + '...' : null,
            mallId: cache.mallId
        });
        
        return {
            working: true,
            cache: cache
        };
    } else {
        console.log('❌ 早期拦截器未找到');
        return {
            working: false,
            cache: null
        };
    }
}

// 4. 检查localStorage中的缓存
function checkStoredCache() {
    console.log('💾 检查localStorage缓存...');
    
    const caches = {
        // 早期拦截器的缓存
        earlyAntiContent: localStorage.getItem('temu_cached_anti_content'),
        earlyMallId: localStorage.getItem('temu_cached_mall_id'),
        
        // 优化测试的缓存
        optimizedAntiContent: localStorage.getItem('optimized_anti_content'),
        optimizedMallId: localStorage.getItem('optimized_mall_id'),
        
        // 终极调试的缓存
        ultimateAntiContent: localStorage.getItem('ultimate_anti_content'),
        ultimateMallId: localStorage.getItem('ultimate_mall_id')
    };
    
    console.log('📋 所有缓存状态:', caches);
    
    // 找到最新的有效缓存
    let latestAntiContent = null;
    let latestMallId = null;
    
    if (caches.ultimateAntiContent) {
        latestAntiContent = caches.ultimateAntiContent;
        console.log('🎯 使用终极调试缓存的 anti-content');
    } else if (caches.earlyAntiContent) {
        latestAntiContent = caches.earlyAntiContent;
        console.log('🎯 使用早期拦截器缓存的 anti-content');
    } else if (caches.optimizedAntiContent) {
        latestAntiContent = caches.optimizedAntiContent;
        console.log('🎯 使用优化测试缓存的 anti-content');
    }
    
    if (caches.ultimateMallId) {
        latestMallId = caches.ultimateMallId;
        console.log('🎯 使用终极调试缓存的 mallId');
    } else if (caches.earlyMallId) {
        latestMallId = caches.earlyMallId;
        console.log('🎯 使用早期拦截器缓存的 mallId');
    } else if (caches.optimizedMallId) {
        latestMallId = caches.optimizedMallId;
        console.log('🎯 使用优化测试缓存的 mallId');
    }
    
    return {
        caches,
        latest: {
            antiContent: latestAntiContent,
            mallId: latestMallId
        }
    };
}

// 5. 测试API请求
function testApiWithCache() {
    console.log('🧪 使用缓存测试API请求...');
    
    const cacheResult = checkStoredCache();
    const { antiContent, mallId } = cacheResult.latest;
    
    if (!antiContent || !mallId) {
        console.warn('❌ 缺少必要的缓存数据');
        console.log('💡 请先在页面中进行一些操作触发API请求');
        return;
    }
    
    console.log('🚀 使用缓存数据测试API...');
    console.log('🔑 anti-content:', antiContent.substring(0, 50) + '...');
    console.log('🏪 mallId:', mallId);
    
    // 测试待办事项API
    fetch('https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount', {
        method: 'POST',
        headers: {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'anti-content': antiContent,
            'cache-control': 'max-age=0',
            'content-type': 'application/json',
            'mallid': mallId,
            'origin': 'https://seller.kuajingmaihuo.com',
            'referer': 'https://seller.kuajingmaihuo.com/main/product/seller-select',
            'user-agent': navigator.userAgent
        },
        credentials: 'include',
        body: '{}'
    }).then(response => {
        console.log('🧪 API响应状态:', response.status);
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    }).then(data => {
        console.log('✅ API测试成功:', data);
    }).catch(error => {
        console.error('❌ API测试失败:', error);
    });
}

// 6. 生成简化报告
function generateOptimizedReport() {
    console.log('📊 生成优化报告...');
    
    const earlyInterceptor = checkEarlyInterceptor();
    const cacheResult = checkStoredCache();
    
    const report = {
        timestamp: new Date().toLocaleString(),
        pageUrl: window.location.href,
        earlyInterceptor: earlyInterceptor,
        cacheStatus: cacheResult,
        interceptedRequests: optimizedTest.interceptedRequests.length,
        currentSession: {
            antiContentFound: optimizedTest.antiContentFound,
            mallIdFound: optimizedTest.mallIdFound
        },
        requests: optimizedTest.interceptedRequests
    };
    
    console.log('📊 优化报告:', report);
    return report;
}

// 主函数
function runOptimizedTest() {
    console.log('🚀 启动优化测试...');
    
    // 1. 设置拦截
    setupOptimizedInterception();
    
    // 2. 检查现有状态
    checkEarlyInterceptor();
    checkStoredCache();
    
    // 3. 测试API
    setTimeout(testApiWithCache, 2000);
    
    console.log('✅ 优化测试设置完成');
    console.log('💡 请在页面中进行操作，然后运行 optimizedTest.report() 查看结果');
    console.log('💡 运行 optimizedTest.testApi() 测试API请求');
}

// 暴露全局函数
window.optimizedTest = {
    run: runOptimizedTest,
    report: generateOptimizedReport,
    testApi: testApiWithCache,
    checkCache: checkStoredCache,
    checkInterceptor: checkEarlyInterceptor,
    state: optimizedTest
};

console.log('🔧 优化测试工具已准备就绪！');
console.log('💡 运行 optimizedTest.run() 开始测试');
console.log('💡 运行 optimizedTest.report() 查看报告');
console.log('💡 运行 optimizedTest.testApi() 测试API');

// 自动开始
runOptimizedTest();
