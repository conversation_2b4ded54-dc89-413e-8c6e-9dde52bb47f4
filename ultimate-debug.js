// 终极anti-content调试脚本 - 解决第三方JS库请求拦截问题
// 在Temu商家后台页面控制台运行

console.log('🔥 启动终极anti-content调试脚本...');

// 全局状态
const ultimateDebug = {
    interceptedRequests: [],
    antiContentFound: null,
    mallIdFound: null,
    interceptors: {
        fetch: null,
        xhr: null,
        jquery: null,
        axios: null
    }
};

// 1. 深度拦截所有可能的请求方式
function setupUltimateInterception() {
    console.log('🌐 设置终极网络拦截...');
    
    // 拦截原生fetch
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const [url, options] = args;
        const urlString = typeof url === 'string' ? url : url.toString();
        
        if (urlString.includes('seller.kuajingmaihuo.com')) {
            console.log('🎯 拦截fetch请求:', urlString);
            console.log('📋 请求选项:', options);
            
            if (options && options.headers) {
                checkHeaders(options.headers, 'fetch', urlString);
            }
        }
        
        return originalFetch.apply(this, args);
    };
    
    // 拦截XMLHttpRequest的所有方法
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSetHeader = XMLHttpRequest.prototype.setRequestHeader;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    const xhrInstances = new WeakMap();
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        const urlString = url.toString();
        xhrInstances.set(this, { 
            method, 
            url: urlString, 
            headers: {},
            timestamp: Date.now()
        });
        
        if (urlString.includes('seller.kuajingmaihuo.com')) {
            console.log('🎯 XHR open:', method, urlString);
        }
        
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
        const instance = xhrInstances.get(this);
        if (instance) {
            instance.headers[name] = value;
            
            if (instance.url.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 XHR设置头部:', name, '=', value.substring(0, 100) + (value.length > 100 ? '...' : ''));
                checkSingleHeader(name, value, 'XHR', instance.url);
            }
        }
        
        return originalXHRSetHeader.call(this, name, value);
    };
    
    XMLHttpRequest.prototype.send = function(body) {
        const instance = xhrInstances.get(this);
        if (instance && instance.url.includes('seller.kuajingmaihuo.com')) {
            console.log('🎯 XHR发送请求:', instance.method, instance.url);
            console.log('📋 所有头部:', instance.headers);
            checkHeaders(instance.headers, 'XHR', instance.url);
        }
        
        return originalXHRSend.call(this, body);
    };
    
    // 拦截jQuery AJAX（如果存在）
    if (window.$ && window.$.ajaxSetup) {
        console.log('🎯 检测到jQuery，设置AJAX拦截...');
        
        const originalAjax = window.$.ajax;
        window.$.ajax = function(options) {
            if (options.url && options.url.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 拦截jQuery AJAX:', options.url);
                console.log('📋 jQuery选项:', options);
                
                if (options.headers) {
                    checkHeaders(options.headers, 'jQuery', options.url);
                }
            }
            
            return originalAjax.apply(this, arguments);
        };
    }
    
    // 拦截axios（如果存在）
    if (window.axios) {
        console.log('🎯 检测到axios，设置拦截器...');
        
        window.axios.interceptors.request.use(function (config) {
            if (config.url && config.url.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 拦截axios请求:', config.url);
                console.log('📋 axios配置:', config);
                
                if (config.headers) {
                    checkHeaders(config.headers, 'axios', config.url);
                }
            }
            return config;
        });
    }
    
    console.log('✅ 终极网络拦截设置完成');
}

// 2. 检查请求头的通用函数
function checkHeaders(headers, source, url) {
    console.log(`📋 检查${source}请求头:`, headers);
    
    // 检查anti-content的所有可能变体
    const antiContentKeys = [
        'anti-content', 'Anti-Content', 'ANTI-CONTENT', 'antiContent',
        'anti_content', 'Anti_Content', 'ANTI_CONTENT'
    ];
    
    let foundAntiContent = null;
    let foundKey = null;
    
    for (const key of antiContentKeys) {
        if (headers[key]) {
            foundAntiContent = headers[key];
            foundKey = key;
            break;
        }
    }
    
    if (foundAntiContent) {
        ultimateDebug.antiContentFound = foundAntiContent;
        console.log(`🎉 找到 anti-content (${source}, key: ${foundKey}):`, foundAntiContent);
        
        // 保存到localStorage
        try {
            localStorage.setItem('ultimate_anti_content', foundAntiContent);
            localStorage.setItem('ultimate_anti_content_source', source);
            localStorage.setItem('ultimate_anti_content_key', foundKey);
            localStorage.setItem('ultimate_anti_content_time', Date.now().toString());
            localStorage.setItem('ultimate_anti_content_url', url);
        } catch (e) {
            console.warn('保存anti-content失败:', e);
        }
    }
    
    // 检查mallid的所有可能变体
    const mallIdKeys = ['mallid', 'mallId', 'MallId', 'MALLID', 'mall_id', 'Mall_Id'];
    
    let foundMallId = null;
    let foundMallKey = null;
    
    for (const key of mallIdKeys) {
        if (headers[key]) {
            foundMallId = headers[key];
            foundMallKey = key;
            break;
        }
    }
    
    if (foundMallId) {
        ultimateDebug.mallIdFound = foundMallId;
        console.log(`🎉 找到 mallId (${source}, key: ${foundMallKey}):`, foundMallId);
        
        // 保存到localStorage
        try {
            localStorage.setItem('ultimate_mall_id', foundMallId);
            localStorage.setItem('ultimate_mall_id_source', source);
            localStorage.setItem('ultimate_mall_id_key', foundMallKey);
            localStorage.setItem('ultimate_mall_id_time', Date.now().toString());
        } catch (e) {
            console.warn('保存mallId失败:', e);
        }
    }
    
    // 记录请求
    ultimateDebug.interceptedRequests.push({
        source,
        url,
        headers,
        antiContent: foundAntiContent,
        mallId: foundMallId,
        timestamp: Date.now()
    });
}

// 3. 检查单个请求头
function checkSingleHeader(name, value, source, url) {
    const lowerName = name.toLowerCase();
    
    if (lowerName.includes('anti') && lowerName.includes('content')) {
        ultimateDebug.antiContentFound = value;
        console.log(`🎉 找到 anti-content (${source}, header: ${name}):`, value);
        
        try {
            localStorage.setItem('ultimate_anti_content', value);
            localStorage.setItem('ultimate_anti_content_source', source);
            localStorage.setItem('ultimate_anti_content_key', name);
            localStorage.setItem('ultimate_anti_content_time', Date.now().toString());
            localStorage.setItem('ultimate_anti_content_url', url);
        } catch (e) {}
    }
    
    if (lowerName.includes('mall') && lowerName.includes('id')) {
        ultimateDebug.mallIdFound = value;
        console.log(`🎉 找到 mallId (${source}, header: ${name}):`, value);
        
        try {
            localStorage.setItem('ultimate_mall_id', value);
            localStorage.setItem('ultimate_mall_id_source', source);
            localStorage.setItem('ultimate_mall_id_key', name);
            localStorage.setItem('ultimate_mall_id_time', Date.now().toString());
        } catch (e) {}
    }
}

// 4. 监控所有可能的网络库
function monitorNetworkLibraries() {
    console.log('📚 监控网络库...');
    
    // 监控新加载的脚本
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'SCRIPT') {
                    console.log('📜 检测到新脚本:', node.src || '内联脚本');
                    
                    // 延迟检查是否有新的网络库
                    setTimeout(() => {
                        if (window.axios && !ultimateDebug.interceptors.axios) {
                            console.log('🎯 检测到新的axios，重新设置拦截器...');
                            setupAxiosInterceptor();
                        }
                        
                        if (window.$ && !ultimateDebug.interceptors.jquery) {
                            console.log('🎯 检测到新的jQuery，重新设置拦截器...');
                            setupJQueryInterceptor();
                        }
                    }, 1000);
                }
            });
        });
    });
    
    observer.observe(document.head, { childList: true, subtree: true });
    observer.observe(document.body, { childList: true, subtree: true });
}

// 5. 测试已知的anti-content
function testKnownAntiContent() {
    console.log('🧪 测试已知的anti-content...');
    
    const knownAntiContent = '0aqWfxUeMwVElKpCXDN5o6HXxLBgJPpzpbqcUJZEWZSRu4bsgUJOHxSJ9IFWjUWXwbsyhyOmxMo9W-zPQyd_QiSCOQb2dUD2nGnpJFEoV61td0Lg32W9ITD9hFcTQKiF0b01-NYcUa0ykNEYYL0NPRR2nsoBkN9LuDXSx5C9lSoYnNEoa01QNaDBvcCVWYixYU3suZApdqGVCTu-UkMmd0LggODyjFVCEc-4cXAPQ8Aepmpl39NLjtM-cuN0GcejYsn2cf7zmuYGTm_HjUJX8ysy5oEmUni2ittPPANWOr0XGnserMsAdef_dBx-m3Ruz1lpMIw7Se1E-eM7S1hEMeBkm3WuSLhQ14e1jiT-fi_M-a4vkdG1stp_xZysVO-lUpaAJ2bq2FeBwIeM2IkB2Ikz15EzwUIB1ImBZ5mMfOmM-MkBaEylgEEBtLJ_tYXH4YFIuUJYkDtYX-fj0CvXj0T4XDX_NFoGjma4jgann9YOsE9415ez4cdMf-KtUVzsl_FZIuMWzWzLfOkMROeMq-KW6hgRQHKtlwFeIuIhtuMeRAHtUZSsl7S1hKm9-dEVt24uBfkE-gMvgh0wFw2mMwlT71ZZt--d-325sWgemx77-ZLI1-7hKv7eS-xU--9ezw5ZBF6y_KSI2PvuVOFS6QiS9G0X3YqIbqXdNIADsP5_qOuy8jcvY1cE3YWuWjirTdB9XPorLfM7OlWD';
    const knownMallId = '634418223971228';
    
    // 手动保存已知值
    try {
        localStorage.setItem('ultimate_anti_content', knownAntiContent);
        localStorage.setItem('ultimate_anti_content_source', 'manual');
        localStorage.setItem('ultimate_anti_content_key', 'anti-content');
        localStorage.setItem('ultimate_anti_content_time', Date.now().toString());
        localStorage.setItem('ultimate_mall_id', knownMallId);
        localStorage.setItem('ultimate_mall_id_source', 'manual');
        localStorage.setItem('ultimate_mall_id_key', 'mallid');
        localStorage.setItem('ultimate_mall_id_time', Date.now().toString());
        
        console.log('✅ 已保存已知的anti-content和mallId');
    } catch (e) {
        console.warn('保存已知值失败:', e);
    }
    
    // 测试API请求
    console.log('🧪 测试API请求...');
    fetch('https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/venom/purchase/order/queryRedNotice', {
        method: 'POST',
        headers: {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'anti-content': knownAntiContent,
            'cache-control': 'max-age=0',
            'content-type': 'application/json',
            'mallid': knownMallId,
            'origin': 'https://seller.kuajingmaihuo.com',
            'priority': 'u=1, i',
            'referer': 'https://seller.kuajingmaihuo.com/main/product/seller-select',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        },
        credentials: 'include',
        body: '{}'
    }).then(response => {
        console.log('🧪 测试API响应状态:', response.status);
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    }).then(data => {
        console.log('✅ 测试API成功:', data);
    }).catch(error => {
        console.error('❌ 测试API失败:', error);
    });
}

// 6. 生成详细报告
function generateUltimateReport() {
    console.log('📊 生成终极调试报告...');
    
    const report = {
        timestamp: new Date().toLocaleString(),
        pageUrl: window.location.href,
        interceptedRequests: ultimateDebug.interceptedRequests.length,
        antiContentFound: ultimateDebug.antiContentFound,
        mallIdFound: ultimateDebug.mallIdFound,
        localStorage: {
            antiContent: localStorage.getItem('ultimate_anti_content'),
            antiContentSource: localStorage.getItem('ultimate_anti_content_source'),
            antiContentKey: localStorage.getItem('ultimate_anti_content_key'),
            mallId: localStorage.getItem('ultimate_mall_id'),
            mallIdSource: localStorage.getItem('ultimate_mall_id_source'),
            mallIdKey: localStorage.getItem('ultimate_mall_id_key')
        },
        networkLibraries: {
            jquery: !!window.$,
            axios: !!window.axios,
            fetch: !!window.fetch
        },
        requests: ultimateDebug.interceptedRequests
    };
    
    console.log('📊 终极调试报告:', report);
    return report;
}

// 主函数
function runUltimateDebug() {
    console.log('🚀 启动终极调试...');
    
    // 1. 设置拦截
    setupUltimateInterception();
    
    // 2. 监控网络库
    monitorNetworkLibraries();
    
    // 3. 测试已知值
    testKnownAntiContent();
    
    // 4. 定期生成报告
    setInterval(generateUltimateReport, 30000);
    
    console.log('✅ 终极调试设置完成');
    console.log('💡 请在页面中进行操作，然后运行 ultimateDebug.report() 查看结果');
}

// 暴露全局函数
window.ultimateDebug = {
    run: runUltimateDebug,
    report: generateUltimateReport,
    test: testKnownAntiContent,
    state: ultimateDebug
};

console.log('🔧 终极调试工具已准备就绪！');
console.log('💡 运行 ultimateDebug.run() 开始调试');
console.log('💡 运行 ultimateDebug.report() 查看报告');
console.log('💡 运行 ultimateDebug.test() 测试已知值');

// 自动开始
runUltimateDebug();
