// ================================================================
//  Content Script 主文件 (temu-main.ts)
// ================================================================

// PART 1: 注入逻辑 (已移除)
// 注入工作现在由 manifest.json 通过 "world": "MAIN" 声明式地完成。
// 这种方式更早、更可靠，无需在此处编写任何注入代码。
console.log('[Temu Main] Content-Script 加载。拦截器已由 manifest 自动注入到页面。');


// PART 2: 业务逻辑与数据消费者
interface TemuSiteInfo {
  fromPlat: string;
  mallId: string | number;
  shopId: string | number;
  mallName: string;
  shopName:string;
  mallStatus: number;
  isSemiManagedMall: boolean;
  logo: string;
}

class TemuDetector {
  // 此方法从 localStorage 读取数据，这是拦截器脚本和本脚本之间的桥梁
  private getCachedHeader(key: 'anti_content' | 'mall_id'): string | null {
    const value = localStorage.getItem(`temu_cs_${key}`);
    const expiryStr = localStorage.getItem(`temu_cs_${key}_expiry`);
    if (value && expiryStr && Date.now() < parseInt(expiryStr, 10)) {
      return value;
    }
    // 如果过期，则清除
    if (value) {
      localStorage.removeItem(`temu_cs_${key}`);
      localStorage.removeItem(`temu_cs_${key}_expiry`);
    }
    return null;
  }

  public async fetchTemuUserInfo(): Promise<TemuSiteInfo | null> {
    const antiContent = this.getCachedHeader('anti_content');
    if (!antiContent) {
      console.warn('[Temu Detector] 无法获取 userInfo，因为 anti-content 未被捕获或已过期。等待页面下一次请求...');
      return null;
    }

    try {
      const response = await fetch('https://seller.kuajingmaihuo.com/bg/quiet/api/mms/userInfo', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'anti-content': antiContent,
        },
        body: JSON.stringify({})
      });

      if (!response.ok) throw new Error(`API 请求失败: ${response.status}`);
      const data = await response.json();
      return this.parseShopData(data);
    } catch (error) {
      console.error('[Temu Detector] fetchTemuUserInfo 失败:', error);
      return null;
    }
  }

  private parseShopData(data: any): TemuSiteInfo | null {
    if (!data?.success || !data.result?.companyList) return null;

    for (const company of data.result.companyList) {
      if (company?.malInfoList) {
        for (const mallInfo of company.malInfoList) {
          if (mallInfo?.mallId && mallInfo?.mallName) {
            console.log('[Temu Detector] 成功解析到店铺信息。');
            return {
              fromPlat: 'temu',
              mallId: mallInfo.mallId,
              shopId: mallInfo.mallId,
              mallName: mallInfo.mallName,
              shopName: mallInfo.mallName,
              mallStatus: mallInfo.mallStatus || 1,
              isSemiManagedMall: mallInfo.isSemiManagedMall || false,
              logo: mallInfo.logo || ''
            };
          }
        }
      }
    }
    console.warn('[Temu Detector] 未能在API响应中解析到店铺信息。');
    return null;
  }
  
  public async getTemuData(apiType: string, params: any, mallId?: string): Promise<any> {
    const antiContent = this.getCachedHeader('anti_content');
    const finalMallId = mallId || this.getCachedHeader('mall_id');

    if (!antiContent || !finalMallId) {
      throw new Error('缺少 anti-content 或 mallId，无法请求数据。请刷新页面或进行操作以触发捕获。');
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'anti-content': antiContent,
      'mallid': finalMallId,
    };

    let url: string;
    let body: any;

    switch (apiType) {
      case 'todo':
        url = 'https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount';
        body = JSON.stringify({});
        break;
      case 'products':
        url = 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier';
        body = JSON.stringify({ pageSize: 50, pageNum: 1, ...params });
        break;
      default:
        throw new Error(`不支持的 API 类型: ${apiType}`);
    }

    const response = await fetch(url, {
      method: 'POST',
      credentials: 'include',
      headers,
      body,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }
}

// PART 3: 启动与通信
function initialize() {
  const detector = new TemuDetector();
  (window as any).temuGlobalDetector = detector; // 暴露到 content-script 的 window，方便调试
  console.log('[Temu Main] TemuDetector 已初始化。');
  
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('[Temu Main] 收到消息:', request);
    
    (async () => {
      try {
        switch (request.action) {
          case 'GET_TEMU_USER_INFO':
            const userInfo = await detector.fetchTemuUserInfo();
            if (userInfo) {
              await chrome.storage.local.set({ temuSiteInfo: userInfo });
              sendResponse({ success: true, data: userInfo });
            } else {
              sendResponse({ success: false, error: '未能获取用户信息，可能是 anti-content 未捕获' });
            }
            break;
            
          case 'GET_TEMU_DATA':
            if (!request.apiType || !request.params) {
              sendResponse({ success: false, error: '缺少 apiType 或 params' });
              return;
            }
            const data = await detector.getTemuData(request.apiType, request.params, request.mallId);
            sendResponse({ success: true, data });
            break;

          default:
            sendResponse({ success: false, error: '未知的 action' });
            break;
        }
      } catch (error: any) {
        console.error(`[Temu Main] 处理消息 ${request.action} 时出错:`, error);
        sendResponse({ success: false, error: error.message || '未知错误' });
      }
    })();

    return true; // 保持消息通道开放以进行异步响应
  });

  const autoFetchUserInfo = async () => {
      // 检查localStorage中是否有anti-content
      const antiContent = localStorage.getItem('temu_cs_anti_content');
      // 检查chrome.storage中是否已有用户信息
      const alreadyFetched = (await chrome.storage.local.get('temuSiteInfo')).temuSiteInfo;

      if (antiContent && !alreadyFetched) {
          console.log('[Temu Main] 检测到 anti-content 且暂无店铺信息，尝试自动获取...');
          const userInfo = await detector.fetchTemuUserInfo();
          if (userInfo) {
              await chrome.storage.local.set({ temuSiteInfo: userInfo });
              console.log('[Temu Main] 自动获取店铺信息成功！');
          }
      }
  };
  
  // 每10秒检查一次是否可以自动获取用户信息
  setInterval(autoFetchUserInfo, 10000); 
}

// 确保在DOM加载完成后再初始化业务逻辑，这是一种稳健的做法。
if (window.self === window.top) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }
}