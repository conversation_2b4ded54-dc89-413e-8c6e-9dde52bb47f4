// 测试缓存机制的脚本
// 在 Temu 商家后台页面的控制台中运行此脚本

console.log('🧪 开始测试缓存机制...');

// 模拟一个包含anti-content和mallid的请求
function simulateApiRequest() {
    console.log('📡 模拟API请求...');
    
    // 模拟的anti-content和mallid值
    const mockAntiContent = 'mock_anti_content_' + Date.now();
    const mockMallId = 'mock_mall_id_' + Math.random().toString(36).substr(2, 9);
    
    console.log('🔑 模拟的 anti-content:', mockAntiContent);
    console.log('🏪 模拟的 mallId:', mockMallId);
    
    // 模拟fetch请求
    const originalFetch = window.fetch;
    
    // 创建一个模拟的fetch请求
    const mockFetch = async (url, options) => {
        console.log('🌐 拦截到fetch请求:', url);
        console.log('📋 请求选项:', options);
        
        // 返回一个模拟的响应
        return {
            ok: true,
            status: 200,
            json: async () => ({ success: true, data: 'mock data' })
        };
    };
    
    // 发起一个包含anti-content和mallid的模拟请求
    mockFetch('https://seller.kuajingmaihuo.com/test-api', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'anti-content': mockAntiContent,
            'mallid': mockMallId
        },
        body: JSON.stringify({ test: true })
    });
    
    return { mockAntiContent, mockMallId };
}

// 检查localStorage中的缓存
function checkCache() {
    console.log('🔍 检查localStorage缓存...');
    
    const cachedAntiContent = localStorage.getItem('temu_cached_anti_content');
    const antiContentExpiry = localStorage.getItem('temu_anti_content_expiry');
    const cachedMallId = localStorage.getItem('temu_cached_mall_id');
    const mallIdExpiry = localStorage.getItem('temu_mall_id_expiry');
    
    console.log('💾 缓存的 anti-content:', cachedAntiContent);
    console.log('⏰ anti-content 过期时间:', antiContentExpiry ? new Date(parseInt(antiContentExpiry)).toLocaleString() : 'N/A');
    console.log('💾 缓存的 mallId:', cachedMallId);
    console.log('⏰ mallId 过期时间:', mallIdExpiry ? new Date(parseInt(mallIdExpiry)).toLocaleString() : 'N/A');
    
    // 检查是否过期
    const now = Date.now();
    const antiContentValid = antiContentExpiry && parseInt(antiContentExpiry) > now;
    const mallIdValid = mallIdExpiry && parseInt(mallIdExpiry) > now;
    
    console.log('✅ anti-content 有效:', antiContentValid);
    console.log('✅ mallId 有效:', mallIdValid);
    
    return {
        cachedAntiContent,
        cachedMallId,
        antiContentValid,
        mallIdValid
    };
}

// 手动设置缓存（用于测试）
function setCacheManually() {
    console.log('🔧 手动设置缓存...');
    
    const testAntiContent = 'test_anti_content_' + Date.now();
    const testMallId = 'test_mall_id_' + Math.random().toString(36).substr(2, 9);
    
    // 设置30分钟后过期
    const antiContentExpiry = Date.now() + 30 * 60 * 1000;
    // 设置1小时后过期
    const mallIdExpiry = Date.now() + 60 * 60 * 1000;
    
    localStorage.setItem('temu_cached_anti_content', testAntiContent);
    localStorage.setItem('temu_anti_content_expiry', antiContentExpiry.toString());
    localStorage.setItem('temu_cached_mall_id', testMallId);
    localStorage.setItem('temu_mall_id_expiry', mallIdExpiry.toString());
    
    console.log('✅ 手动设置完成');
    console.log('🔑 测试 anti-content:', testAntiContent);
    console.log('🏪 测试 mallId:', testMallId);
    
    return { testAntiContent, testMallId };
}

// 清除缓存
function clearCache() {
    console.log('🗑️ 清除缓存...');
    
    localStorage.removeItem('temu_cached_anti_content');
    localStorage.removeItem('temu_anti_content_expiry');
    localStorage.removeItem('temu_cached_mall_id');
    localStorage.removeItem('temu_mall_id_expiry');
    
    console.log('✅ 缓存已清除');
}

// 测试扩展的缓存功能
async function testExtensionCache() {
    console.log('🔌 测试扩展缓存功能...');
    
    // 检查是否有扩展的检测器
    if (window.temuShopDetector) {
        console.log('✅ 找到 temuShopDetector');
        
        // 测试获取缓存的anti-content
        const antiContent = window.temuShopDetector.getAntiContentHeader();
        console.log('🔑 扩展获取的 anti-content:', antiContent);
        
        // 测试获取缓存的mallId
        const mallId = window.temuShopDetector.getCachedMallId();
        console.log('🏪 扩展获取的 mallId:', mallId);
        
        return { antiContent, mallId };
    } else {
        console.log('❌ 未找到 temuShopDetector，请确保扩展已加载');
        return null;
    }
}

// 运行完整测试
async function runFullTest() {
    console.log('🚀 开始完整测试...');
    console.log('='.repeat(50));
    
    // 1. 检查初始状态
    console.log('1️⃣ 检查初始缓存状态');
    checkCache();
    
    console.log('\n' + '-'.repeat(30) + '\n');
    
    // 2. 手动设置缓存
    console.log('2️⃣ 手动设置测试缓存');
    const { testAntiContent, testMallId } = setCacheManually();
    
    console.log('\n' + '-'.repeat(30) + '\n');
    
    // 3. 验证缓存
    console.log('3️⃣ 验证缓存设置');
    const cacheResult = checkCache();
    
    console.log('\n' + '-'.repeat(30) + '\n');
    
    // 4. 测试扩展功能
    console.log('4️⃣ 测试扩展缓存功能');
    const extensionResult = await testExtensionCache();
    
    console.log('\n' + '-'.repeat(30) + '\n');
    
    // 5. 验证结果
    console.log('5️⃣ 验证测试结果');
    if (extensionResult) {
        const antiContentMatch = extensionResult.antiContent === testAntiContent;
        const mallIdMatch = extensionResult.mallId === testMallId;
        
        console.log('✅ anti-content 匹配:', antiContentMatch);
        console.log('✅ mallId 匹配:', mallIdMatch);
        
        if (antiContentMatch && mallIdMatch) {
            console.log('🎉 缓存机制测试成功！');
        } else {
            console.log('❌ 缓存机制测试失败');
        }
    } else {
        console.log('❌ 无法测试扩展功能');
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('🏁 测试完成');
}

// 提供给用户的函数
window.testCacheMechanism = {
    runFullTest,
    checkCache,
    setCacheManually,
    clearCache,
    testExtensionCache,
    simulateApiRequest
};

console.log('🔧 缓存测试工具已准备就绪！');
console.log('💡 使用方法：');
console.log('  - testCacheMechanism.runFullTest() - 运行完整测试');
console.log('  - testCacheMechanism.checkCache() - 检查当前缓存');
console.log('  - testCacheMechanism.setCacheManually() - 手动设置测试缓存');
console.log('  - testCacheMechanism.clearCache() - 清除所有缓存');
console.log('  - testCacheMechanism.testExtensionCache() - 测试扩展缓存功能');

// 自动运行一次完整测试
console.log('\n🚀 自动运行完整测试...');
runFullTest();
