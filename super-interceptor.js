// 超级拦截器 - 确保能拦截到所有AJAX请求
// 在Temu页面控制台运行，立即设置拦截器

console.log('🚀 启动超级拦截器...');

// 立即保存原始方法，防止被覆盖
const ORIGINAL_METHODS = {
    fetch: window.fetch,
    xhrOpen: XMLHttpRequest.prototype.open,
    xhrSetHeader: XMLHttpRequest.prototype.setRequestHeader,
    xhrSend: XMLHttpRequest.prototype.send
};

// 全局状态
const superInterceptor = {
    interceptedRequests: [],
    antiContentFound: null,
    mallIdFound: null
};

// 检查和保存anti-content
function saveAntiContent(antiContent, source, key) {
    if (antiContent && antiContent !== superInterceptor.antiContentFound) {
        superInterceptor.antiContentFound = antiContent;
        
        console.log(`🎉 超级拦截器找到 anti-content (${source}, key: ${key}):`, antiContent.substring(0, 50) + '...');
        
        // 保存到多个localStorage键
        try {
            localStorage.setItem('super_anti_content', antiContent);
            localStorage.setItem('ultimate_anti_content', antiContent);
            localStorage.setItem('temu_cached_anti_content', antiContent);
            localStorage.setItem('super_anti_content_time', Date.now().toString());
            localStorage.setItem('super_anti_content_source', source);
            localStorage.setItem('super_anti_content_key', key);
            
            console.log('✅ 超级拦截器已保存 anti-content 到localStorage');
            
            // 通知成功
            alert('🎉 成功获取到 anti-content！扩展现在应该能正常工作了。');
            
        } catch (e) {
            console.warn('保存anti-content失败:', e);
        }
    }
}

// 检查和保存mallId
function saveMallId(mallId, source, key) {
    if (mallId && mallId !== superInterceptor.mallIdFound) {
        superInterceptor.mallIdFound = mallId;
        
        console.log(`🎉 超级拦截器找到 mallId (${source}, key: ${key}):`, mallId);
        
        // 保存到多个localStorage键
        try {
            localStorage.setItem('super_mall_id', mallId);
            localStorage.setItem('ultimate_mall_id', mallId);
            localStorage.setItem('temu_cached_mall_id', mallId);
            localStorage.setItem('super_mall_id_time', Date.now().toString());
            localStorage.setItem('super_mall_id_source', source);
            localStorage.setItem('super_mall_id_key', key);
            
            console.log('✅ 超级拦截器已保存 mallId 到localStorage');
            
        } catch (e) {
            console.warn('保存mallId失败:', e);
        }
    }
}

// 检查请求头
function checkHeaders(headers, source, url) {
    console.log(`🔍 超级拦截器检查${source}请求头:`, headers);
    
    // 检查anti-content的所有可能变体
    const antiContentKeys = [
        'anti-content', 'Anti-Content', 'ANTI-CONTENT', 'antiContent',
        'anti_content', 'Anti_Content', 'ANTI_CONTENT'
    ];
    
    for (const key of antiContentKeys) {
        if (headers[key]) {
            saveAntiContent(headers[key], source, key);
            break;
        }
    }
    
    // 检查mallid的所有可能变体
    const mallIdKeys = ['mallid', 'mallId', 'MallId', 'MALLID', 'mall_id', 'Mall_Id'];
    
    for (const key of mallIdKeys) {
        if (headers[key]) {
            saveMallId(headers[key], source, key);
            break;
        }
    }
    
    // 记录请求
    superInterceptor.interceptedRequests.push({
        source,
        url,
        headers,
        timestamp: Date.now()
    });
}

// 强制设置fetch拦截器
function setupSuperFetchInterceptor() {
    console.log('🌐 设置超级fetch拦截器...');
    
    // 使用defineProperty强制覆盖
    Object.defineProperty(window, 'fetch', {
        value: function(...args) {
            const [url, options] = args;
            const urlString = typeof url === 'string' ? url : url.toString();
            
            if (urlString.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 超级拦截器拦截fetch请求:', urlString);
                console.log('📋 请求选项:', options);
                
                if (options && options.headers) {
                    checkHeaders(options.headers, 'fetch', urlString);
                }
            }
            
            return ORIGINAL_METHODS.fetch.apply(this, args);
        },
        writable: true,
        configurable: true
    });
    
    console.log('✅ 超级fetch拦截器设置完成');
}

// 强制设置XHR拦截器
function setupSuperXHRInterceptor() {
    console.log('🌐 设置超级XHR拦截器...');
    
    const xhrInstances = new WeakMap();
    
    // 强制覆盖open方法
    Object.defineProperty(XMLHttpRequest.prototype, 'open', {
        value: function(method, url, ...args) {
            const urlString = url.toString();
            xhrInstances.set(this, { 
                method, 
                url: urlString, 
                headers: {},
                timestamp: Date.now()
            });
            
            if (urlString.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 超级拦截器XHR open:', method, urlString);
            }
            
            return ORIGINAL_METHODS.xhrOpen.apply(this, [method, url, ...args]);
        },
        writable: true,
        configurable: true
    });
    
    // 强制覆盖setRequestHeader方法
    Object.defineProperty(XMLHttpRequest.prototype, 'setRequestHeader', {
        value: function(name, value) {
            const instance = xhrInstances.get(this);
            if (instance) {
                instance.headers[name] = value;
                
                if (instance.url.includes('seller.kuajingmaihuo.com')) {
                    console.log('🎯 超级拦截器XHR设置头部:', name, '=', value.substring(0, 100) + (value.length > 100 ? '...' : ''));
                    
                    // 立即检查单个头部
                    const lowerName = name.toLowerCase();
                    if (lowerName.includes('anti') && lowerName.includes('content')) {
                        saveAntiContent(value, 'XHR', name);
                    }
                    if (lowerName.includes('mall') && lowerName.includes('id')) {
                        saveMallId(value, 'XHR', name);
                    }
                }
            }
            
            return ORIGINAL_METHODS.xhrSetHeader.call(this, name, value);
        },
        writable: true,
        configurable: true
    });
    
    // 强制覆盖send方法
    Object.defineProperty(XMLHttpRequest.prototype, 'send', {
        value: function(body) {
            const instance = xhrInstances.get(this);
            if (instance && instance.url.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 超级拦截器XHR发送请求:', instance.method, instance.url);
                console.log('📋 所有头部:', instance.headers);
                checkHeaders(instance.headers, 'XHR', instance.url);
            }
            
            return ORIGINAL_METHODS.xhrSend.call(this, body);
        },
        writable: true,
        configurable: true
    });
    
    console.log('✅ 超级XHR拦截器设置完成');
}

// 监控页面中的所有网络活动
function monitorNetworkActivity() {
    console.log('👀 监控网络活动...');
    
    // 监控Performance API
    if (window.performance && window.performance.getEntriesByType) {
        const checkPerformanceEntries = () => {
            const entries = performance.getEntriesByType('navigation').concat(
                performance.getEntriesByType('resource')
            );
            
            entries.forEach(entry => {
                if (entry.name && entry.name.includes('seller.kuajingmaihuo.com')) {
                    console.log('📊 Performance API检测到请求:', entry.name);
                }
            });
        };
        
        // 立即检查一次
        checkPerformanceEntries();
        
        // 定期检查
        setInterval(checkPerformanceEntries, 5000);
    }
    
    // 监控所有可能的全局变量
    const checkGlobalVars = () => {
        const possibleVars = ['__ANTI_CONTENT__', '__INITIAL_STATE__', 'g_config', '__APP_STATE__'];
        possibleVars.forEach(varName => {
            if (window[varName] !== undefined) {
                console.log(`🔍 发现全局变量 ${varName}:`, window[varName]);
            }
        });
    };
    
    checkGlobalVars();
    setInterval(checkGlobalVars, 10000);
}

// 检查当前状态
function checkCurrentStatus() {
    console.log('📊 超级拦截器状态报告:');
    console.log('拦截的请求数量:', superInterceptor.interceptedRequests.length);
    console.log('找到的anti-content:', superInterceptor.antiContentFound ? superInterceptor.antiContentFound.substring(0, 50) + '...' : 'null');
    console.log('找到的mallId:', superInterceptor.mallIdFound);
    
    // 检查localStorage
    const cacheKeys = ['super_anti_content', 'ultimate_anti_content', 'temu_cached_anti_content'];
    cacheKeys.forEach(key => {
        const value = localStorage.getItem(key);
        if (value) {
            console.log(`✅ localStorage[${key}]:`, value.substring(0, 50) + '...');
        }
    });
    
    return {
        interceptedRequests: superInterceptor.interceptedRequests.length,
        antiContentFound: !!superInterceptor.antiContentFound,
        mallIdFound: !!superInterceptor.mallIdFound
    };
}

// 立即启动超级拦截器
function startSuperInterceptor() {
    console.log('🚀 启动超级拦截器...');
    
    // 立即设置拦截器
    setupSuperFetchInterceptor();
    setupSuperXHRInterceptor();
    
    // 监控网络活动
    monitorNetworkActivity();
    
    // 定期检查状态
    setInterval(checkCurrentStatus, 30000);
    
    console.log('✅ 超级拦截器已启动');
    console.log('💡 现在请在页面中进行操作，拦截器会捕获所有AJAX请求');
    console.log('💡 运行 superInterceptor.checkStatus() 查看状态');
}

// 暴露全局函数
window.superInterceptor = {
    checkStatus: checkCurrentStatus,
    state: superInterceptor
};

// 立即启动
startSuperInterceptor();

// 立即检查一次状态
setTimeout(checkCurrentStatus, 2000);
