// 测试扩展 anti-content 修复
// 在Temu商家后台页面控制台运行

console.log('🧪 测试扩展 anti-content 修复...');

window.testExtensionFix = {
    // 0. 诊断扩展加载状态
    diagnoseExtensionLoading: function() {
        console.log('🔍 诊断扩展加载状态...');
        
        // 检查是否有扩展注入的脚本
        const scripts = Array.from(document.querySelectorAll('script'));
        const extensionScripts = scripts.filter(script => 
            script.src && script.src.includes('chrome-extension')
        );
        
        console.log('🔗 扩展脚本数量:', extensionScripts.length);
        extensionScripts.forEach((script, index) => {
            console.log(`  脚本 ${index + 1}:`, script.src);
        });
        
        // 检查控制台日志
        console.log('📝 请检查控制台是否有以下日志:');
        console.log('  - [Early Interceptor] 🚀 启动终极拦截器');
        console.log('  - [Early Interceptor] ✅ 初始化完成');
        console.log('  - [Early Interceptor] ✅ 全局接口已暴露');
        
        // 检查页面URL是否匹配manifest配置
        const currentUrl = window.location.href;
        console.log('🌐 当前页面URL:', currentUrl);
        
        const expectedPatterns = [
            'seller.temu.com',
            'seller.kuajingmaihuo.com',
            'seller-cn.temu.com'
        ];
        
        const isMatched = expectedPatterns.some(pattern => currentUrl.includes(pattern));
        console.log('✅ URL匹配manifest配置:', isMatched ? '是' : '否');
        
        if (!isMatched) {
            console.warn('⚠️ 当前页面URL可能不匹配扩展的content_scripts配置');
        }
        
        return {
            extensionScripts: extensionScripts.length,
            urlMatched: isMatched,
            currentUrl
        };
    },

    // 1. 检查早期拦截器状态
    checkEarlyInterceptor: function() {
        console.log('🔍 检查早期拦截器状态...');
        
        if (window.temuEarlyInterceptor) {
            console.log('✅ 早期拦截器存在');
            
            const state = window.temuEarlyInterceptor.getState();
            console.log('📊 拦截器状态:', {
                antiContentFound: state.antiContentFound ? state.antiContentFound.substring(0, 50) + '...' : null,
                mallIdFound: state.mallIdFound,
                interceptedRequests: state.interceptedRequests.length
            });
            
            const cachedAntiContent = window.temuEarlyInterceptor.getCachedAntiContent();
            const cachedMallId = window.temuEarlyInterceptor.getCachedMallId();
            
            console.log('💾 缓存状态:', {
                antiContent: cachedAntiContent ? cachedAntiContent.substring(0, 50) + '...' : null,
                mallId: cachedMallId
            });
            
            return { state, cachedAntiContent, cachedMallId };
        } else {
            console.log('❌ 早期拦截器不存在');
            return null;
        }
    },

    // 2. 检查localStorage缓存
    checkLocalStorageCache: function() {
        console.log('💾 检查localStorage缓存...');
        
        const sources = [
            'ultimate_anti_content',
            'temu_cached_anti_content',
            'super_anti_content',
            'ultimate_mall_id',
            'temu_cached_mall_id'
        ];
        
        const cacheData = {};
        let hasValidCache = false;
        
        sources.forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                if (key.includes('anti_content')) {
                    cacheData[key] = value.substring(0, 50) + '...';
                } else {
                    cacheData[key] = value;
                }
                hasValidCache = true;
                console.log(`✅ 找到缓存 ${key}:`, cacheData[key]);
            } else {
                console.log(`❌ 未找到缓存 ${key}`);
            }
        });
        
        return { cacheData, hasValidCache };
    },

    // 3. 手动触发API请求测试拦截
    triggerTestRequest: async function() {
        console.log('🎯 手动触发测试请求...');
        
        try {
            // 获取当前最新的anti-content
            let antiContent = null;
            
            // 从早期拦截器获取
            if (window.temuEarlyInterceptor) {
                antiContent = window.temuEarlyInterceptor.getCachedAntiContent();
            }
            
            // 从localStorage获取
            if (!antiContent) {
                const sources = ['ultimate_anti_content', 'temu_cached_anti_content', 'super_anti_content'];
                for (const source of sources) {
                    const cached = localStorage.getItem(source);
                    if (cached && cached.length > 10) {
                        antiContent = cached;
                        break;
                    }
                }
            }
            
            if (!antiContent) {
                console.log('❌ 没有找到有效的 anti-content，无法测试API请求');
                return null;
            }
            
            console.log('🔑 使用 anti-content:', antiContent.substring(0, 50) + '...');
            
            // 构造测试请求
            const headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'anti-content': antiContent,
                'cache-control': 'max-age=0',
                'content-type': 'application/json',
                'origin': 'https://seller.kuajingmaihuo.com',
                'referer': 'https://seller.kuajingmaihuo.com/',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
            };
            
            // 添加mallId（如果有）
            const mallId = localStorage.getItem('ultimate_mall_id') || localStorage.getItem('temu_cached_mall_id');
            if (mallId) {
                headers['mallid'] = mallId;
                console.log('🏪 添加 mallId:', mallId);
            }
            
            const response = await fetch('https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/venom/purchase/order/queryRedNotice', {
                method: 'POST',
                headers: headers,
                credentials: 'include',
                body: JSON.stringify({})
            });
            
            console.log('📤 测试请求发送完成');
            console.log('📥 响应状态:', response.status, response.statusText);
            
            if (response.ok) {
                const data = await response.json();
                console.log('✅ API 响应成功:', data);
                return { success: true, status: response.status, data };
            } else {
                const text = await response.text();
                console.log('❌ API 响应失败:', response.status, text);
                return { success: false, status: response.status, error: text };
            }
        } catch (error) {
            console.error('❌ 测试请求失败:', error);
            return { success: false, error: error.message };
        }
    },    // 4. 综合测试
    runFullTest: async function() {
        console.log('🚀 开始完整测试...');
        
        // 先进行诊断
        const diagnosis = this.diagnoseExtensionLoading();
        
        const interceptorStatus = this.checkEarlyInterceptor();
        const cacheStatus = this.checkLocalStorageCache();
        
        console.log('\n📊 测试总结:');
        console.log('扩展脚本数量:', diagnosis.extensionScripts);
        console.log('URL匹配:', diagnosis.urlMatched ? '✅ 是' : '❌ 否');
        console.log('早期拦截器:', interceptorStatus ? '✅ 正常' : '❌ 异常');
        console.log('localStorage缓存:', cacheStatus.hasValidCache ? '✅ 有效' : '❌ 无效');
        
        // 如果拦截器不存在，尝试启动临时拦截器
        if (!interceptorStatus) {
            console.log('\n🔧 早期拦截器不存在，启动临时拦截器...');
            const tempStatus = this.startTemporaryInterceptor();
            console.log('临时拦截器状态:', tempStatus);
        }
        
        if (interceptorStatus || cacheStatus.hasValidCache) {
            console.log('\n🎯 进行API测试...');
            const apiResult = await this.triggerTestRequest();
            console.log('API测试结果:', apiResult?.success ? '✅ 成功' : '❌ 失败');
            
            return {
                diagnosis,
                interceptor: interceptorStatus,
                cache: cacheStatus,
                api: apiResult
            };
        } else {
            console.log('\n❌ 没有有效的 anti-content，跳过API测试');
            console.log('💡 建议步骤:');
            console.log('1. 在页面中进行一些操作（如查看商品、订单等）');
            console.log('2. 等待几秒钟后重新运行测试');
            console.log('3. 或者手动运行 testExtensionFix.startTemporaryInterceptor()');
            
            return {
                diagnosis,
                interceptor: interceptorStatus,
                cache: cacheStatus,
                api: null
            };
        }
    },

    // 5. 清除所有缓存（用于重新测试）
    clearAllCache: function() {
        console.log('🗑️ 清除所有缓存...');
        
        const keys = [
            'ultimate_anti_content',
            'ultimate_anti_content_source',
            'ultimate_anti_content_key',
            'ultimate_anti_content_time',
            'ultimate_anti_content_url',
            'temu_cached_anti_content',
            'temu_anti_content_expiry',
            'super_anti_content',
            'ultimate_mall_id',
            'temu_cached_mall_id',
            'temu_mall_id_expiry'
        ];
        
        keys.forEach(key => {
            localStorage.removeItem(key);
        });
        
        if (window.temuEarlyInterceptor) {
            window.temuEarlyInterceptor.clearCache();
        }
        
        console.log('✅ 所有缓存已清除');
    },

    // 6. 启动临时拦截器（作为备用方案）
    startTemporaryInterceptor: function() {
        console.log('🔧 启动临时拦截器作为备用方案...');
        
        if (window.temporaryInterceptor) {
            console.log('✅ 临时拦截器已存在');
            return window.temporaryInterceptor.report();
        }
        
        // 如果没有加载临时拦截器脚本，创建一个简化版本
        console.log('⚙️ 创建简化版临时拦截器...');
        
        const originalFetch = window.fetch;
        let antiContentFound = null;
        let mallIdFound = null;
        
        window.fetch = function(...args) {
            const [url, options] = args;
            const urlString = typeof url === 'string' ? url : url.toString();

            if (urlString.includes('seller.kuajingmaihuo.com')) {
                console.log('🎯 [简化拦截器] 拦截请求:', urlString);
                
                if (options && options.headers) {
                    console.log('📋 [简化拦截器] 请求头:', options.headers);
                    
                    // 检查anti-content
                    for (const [key, value] of Object.entries(options.headers)) {
                        if (key.toLowerCase().includes('anti') && key.toLowerCase().includes('content')) {
                            antiContentFound = value;
                            console.log('🎉 [简化拦截器] 找到 anti-content:', value);
                            localStorage.setItem('temu_cached_anti_content', value);
                            localStorage.setItem('temu_anti_content_expiry', (Date.now() + 30 * 60 * 1000).toString());
                        }
                        
                        if (key.toLowerCase().includes('mall') && key.toLowerCase().includes('id')) {
                            mallIdFound = value;
                            console.log('🎉 [简化拦截器] 找到 mallId:', value);
                            localStorage.setItem('temu_cached_mall_id', value);
                            localStorage.setItem('temu_mall_id_expiry', (Date.now() + 60 * 60 * 1000).toString());
                        }
                    }
                }
            }

            return originalFetch.apply(this, args);
        };
        
        // 设置兼容的全局接口
        window.temuEarlyInterceptor = {
            getCachedAntiContent: () => antiContentFound || localStorage.getItem('temu_cached_anti_content'),
            getCachedMallId: () => mallIdFound || localStorage.getItem('temu_cached_mall_id'),
            getState: () => ({
                antiContentFound: antiContentFound || localStorage.getItem('temu_cached_anti_content'),
                mallIdFound: mallIdFound || localStorage.getItem('temu_cached_mall_id'),
                interceptedRequests: []
            }),
            report: () => {
                console.log('📊 简化拦截器报告:', {
                    antiContent: antiContentFound || localStorage.getItem('temu_cached_anti_content'),
                    mallId: mallIdFound || localStorage.getItem('temu_cached_mall_id')
                });
            }
        };
        
        console.log('✅ 简化版临时拦截器设置完成');
        console.log('💡 现在请在页面中进行一些操作，触发API请求');
        
        return { status: 'created', antiContentFound, mallIdFound };
    },

    // 7. 验证扩展功能测试
    testExtensionFunctionality: async function() {
        console.log('🧪 测试扩展功能是否正常...');
        
        // 检查缓存状态
        const cachedAntiContent = localStorage.getItem('temu_cached_anti_content');
        const cachedMallId = localStorage.getItem('temu_cached_mall_id');
        
        console.log('💾 当前缓存状态:');
        console.log('anti-content:', cachedAntiContent ? cachedAntiContent.substring(0, 50) + '...' : 'null');
        console.log('mallId:', cachedMallId);
        
        if (!cachedAntiContent || !cachedMallId) {
            console.log('❌ 缺少必要的缓存，无法测试扩展功能');
            return { success: false, error: '缺少必要的缓存' };
        }
        
        // 测试使用扩展的API调用
        try {
            console.log('🔌 测试扩展API调用...');
            
            // 模拟扩展的API请求
            const headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'anti-content': cachedAntiContent,
                'cache-control': 'max-age=0',
                'content-type': 'application/json',
                'mallid': cachedMallId,
                'origin': 'https://seller.kuajingmaihuo.com',
                'referer': 'https://seller.kuajingmaihuo.com/',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
            };
            
            // 测试1: 获取待办事项数量
            console.log('📋 测试获取待办事项数量...');
            const todoResponse = await fetch('https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount', {
                method: 'POST',
                headers: headers,
                credentials: 'include',
                body: JSON.stringify({
                    pageSize: 10,
                    pageNum: 1,
                    supplierTodoTypeList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
                })
            });
            
            console.log('📤 待办事项请求状态:', todoResponse.status);
            
            if (todoResponse.ok) {
                const todoData = await todoResponse.json();
                console.log('✅ 待办事项数据获取成功:', todoData);
                
                // 测试2: 获取商品列表
                console.log('📦 测试获取商品列表...');
                const productResponse = await fetch('https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier', {
                    method: 'POST',
                    headers: headers,
                    credentials: 'include',
                    body: JSON.stringify({
                        pageSize: 10,
                        pageNum: 1,
                        supplierTodoTypeList: []
                    })
                });
                
                console.log('📤 商品列表请求状态:', productResponse.status);
                
                if (productResponse.ok) {
                    const productData = await productResponse.json();
                    console.log('✅ 商品列表数据获取成功:', productData);
                    
                    return {
                        success: true,
                        todoData: todoData,
                        productData: productData,
                        cachedAntiContent: cachedAntiContent.substring(0, 50) + '...',
                        cachedMallId: cachedMallId
                    };
                } else {
                    const errorText = await productResponse.text();
                    console.log('❌ 商品列表请求失败:', productResponse.status, errorText);
                    return {
                        success: false,
                        error: `商品列表请求失败: ${productResponse.status}`,
                        todoData: todoData
                    };
                }
            } else {
                const errorText = await todoResponse.text();
                console.log('❌ 待办事项请求失败:', todoResponse.status, errorText);
                return {
                    success: false,
                    error: `待办事项请求失败: ${todoResponse.status}`
                };
            }
        } catch (error) {
            console.error('❌ 扩展功能测试失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    // ...existing code...
}

console.log('✅ 测试工具准备完成！');
console.log('🎉 临时拦截器已成功获取到 anti-content 和 mallId！');
console.log('💡 运行 testExtensionFix.testExtensionFunctionality() 验证扩展功能');
console.log('💡 运行 testExtensionFix.runFullTest() 开始完整测试');
console.log('💡 运行 testExtensionFix.diagnoseExtensionLoading() 诊断扩展加载');
console.log('💡 运行 testExtensionFix.checkLocalStorageCache() 检查缓存状态');
console.log('💡 运行 testExtensionFix.triggerTestRequest() 测试API请求');
console.log('💡 运行 testExtensionFix.startTemporaryInterceptor() 启动临时拦截器');
