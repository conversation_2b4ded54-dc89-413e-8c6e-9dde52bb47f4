// 测试扩展修复效果的脚本
// 在Temu商家后台页面控制台运行

console.log('🧪 测试扩展修复效果...');

// 测试函数
const extensionTest = {
    // 1. 检查localStorage中的缓存
    checkCache: function() {
        console.log('💾 检查localStorage缓存...');
        
        const caches = {
            // 各种来源的缓存
            ultimateAntiContent: localStorage.getItem('ultimate_anti_content'),
            ultimateMallId: localStorage.getItem('ultimate_mall_id'),
            earlyAntiContent: localStorage.getItem('temu_cached_anti_content'),
            earlyMallId: localStorage.getItem('temu_cached_mall_id'),
            optimizedAntiContent: localStorage.getItem('optimized_anti_content'),
            optimizedMallId: localStorage.getItem('optimized_mall_id')
        };
        
        console.log('📋 所有缓存:', caches);
        
        // 找到有效的缓存
        const validAntiContent = caches.ultimateAntiContent || caches.earlyAntiContent || caches.optimizedAntiContent;
        const validMallId = caches.ultimateMallId || caches.earlyMallId || caches.optimizedMallId;
        
        if (validAntiContent && validMallId) {
            console.log('✅ 找到有效缓存:');
            console.log('🔑 anti-content:', validAntiContent.substring(0, 50) + '...');
            console.log('🏪 mallId:', validMallId);
            return { antiContent: validAntiContent, mallId: validMallId };
        } else {
            console.log('❌ 没有找到有效缓存');
            return null;
        }
    },

    // 2. 测试扩展的API调用
    testExtensionApi: async function() {
        console.log('🔌 测试扩展API调用...');
        
        try {
            // 检查扩展是否存在
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                console.log('❌ Chrome扩展API不可用');
                return false;
            }
            
            // 尝试发送消息到background script
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'TEST_CONNECTION'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(response);
                    }
                });
            });
            
            console.log('✅ 扩展连接成功:', response);
            return true;
        } catch (error) {
            console.log('❌ 扩展连接失败:', error);
            return false;
        }
    },

    // 3. 模拟扩展的数据获取逻辑
    simulateExtensionLogic: function() {
        console.log('🎭 模拟扩展数据获取逻辑...');
        
        // 模拟TemuDataService的getAntiContentFromStorage逻辑
        const getAntiContentFromStorage = () => {
            const sources = [
                'ultimate_anti_content',
                'temu_cached_anti_content',
                'optimized_anti_content'
            ];
            
            for (const source of sources) {
                const cached = localStorage.getItem(source);
                if (cached) {
                    console.log(`✅ 从 ${source} 获取到 anti-content`);
                    return cached;
                }
            }
            return null;
        };
        
        // 模拟getMallIdFromStorage逻辑
        const getMallIdFromStorage = () => {
            const sources = [
                'ultimate_mall_id',
                'temu_cached_mall_id',
                'optimized_mall_id'
            ];
            
            for (const source of sources) {
                const cached = localStorage.getItem(source);
                if (cached) {
                    console.log(`✅ 从 ${source} 获取到 mallId`);
                    return cached;
                }
            }
            return null;
        };
        
        const antiContent = getAntiContentFromStorage();
        const mallId = getMallIdFromStorage();
        
        if (antiContent && mallId) {
            console.log('✅ 扩展逻辑模拟成功');
            console.log('🔑 获取到 anti-content:', antiContent.substring(0, 50) + '...');
            console.log('🏪 获取到 mallId:', mallId);
            return { antiContent, mallId };
        } else {
            console.log('❌ 扩展逻辑模拟失败');
            return null;
        }
    },

    // 4. 测试API请求（使用扩展逻辑获取的数据）
    testApiWithExtensionData: async function() {
        console.log('🧪 使用扩展逻辑测试API请求...');
        
        const data = this.simulateExtensionLogic();
        if (!data) {
            console.log('❌ 无法获取必要数据，跳过API测试');
            return false;
        }
        
        const { antiContent, mallId } = data;
        
        try {
            console.log('🚀 发送API请求...');
            
            const response = await fetch('https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount', {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'anti-content': antiContent,  // 使用小写键名
                    'cache-control': 'max-age=0',
                    'content-type': 'application/json',
                    'mallid': mallId,  // 使用小写键名
                    'origin': 'https://seller.kuajingmaihuo.com',
                    'referer': 'https://seller.kuajingmaihuo.com/main/product/seller-select',
                    'user-agent': navigator.userAgent
                },
                credentials: 'include',
                body: '{}'
            });
            
            console.log('📡 API响应状态:', response.status);
            
            if (response.ok) {
                const result = await response.json();
                console.log('✅ API请求成功:', result);
                return true;
            } else {
                console.log('❌ API请求失败:', response.status, response.statusText);
                return false;
            }
        } catch (error) {
            console.log('❌ API请求异常:', error);
            return false;
        }
    },

    // 5. 运行完整测试
    runFullTest: async function() {
        console.log('🚀 开始完整测试...');
        console.log('='.repeat(60));
        
        // 1. 检查缓存
        console.log('1️⃣ 检查缓存状态');
        const cacheResult = this.checkCache();
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 2. 测试扩展连接
        console.log('2️⃣ 测试扩展连接');
        const extensionResult = await this.testExtensionApi();
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 3. 模拟扩展逻辑
        console.log('3️⃣ 模拟扩展逻辑');
        const logicResult = this.simulateExtensionLogic();
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 4. 测试API请求
        console.log('4️⃣ 测试API请求');
        const apiResult = await this.testApiWithExtensionData();
        
        console.log('\n' + '='.repeat(60));
        
        // 5. 生成测试报告
        const report = {
            timestamp: new Date().toLocaleString(),
            cache: !!cacheResult,
            extension: extensionResult,
            logic: !!logicResult,
            api: apiResult,
            overall: !!cacheResult && !!logicResult && apiResult
        };
        
        console.log('📊 测试报告:', report);
        
        if (report.overall) {
            console.log('🎉 扩展修复测试通过！');
        } else {
            console.log('❌ 扩展修复测试失败，需要进一步调试');
        }
        
        return report;
    }
};

// 暴露到全局
window.extensionTest = extensionTest;

console.log('🔧 扩展修复测试工具已准备就绪！');
console.log('💡 运行 extensionTest.runFullTest() 开始完整测试');
console.log('💡 运行 extensionTest.checkCache() 检查缓存');
console.log('💡 运行 extensionTest.testApiWithExtensionData() 测试API');

// 自动运行测试
extensionTest.runFullTest();
