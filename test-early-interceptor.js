// 测试早期拦截器的脚本
// 在 Temu 商家后台页面的控制台中运行此脚本

console.log('🧪 开始测试早期拦截器...');

// 检查早期拦截器是否已加载
function checkEarlyInterceptor() {
    console.log('🔍 检查早期拦截器状态...');
    
    if (window.temuEarlyInterceptor) {
        console.log('✅ 早期拦截器已加载');
        
        const cache = window.temuEarlyInterceptor.getCache();
        console.log('💾 当前缓存状态:', cache);
        
        const antiContent = window.temuEarlyInterceptor.getCachedAntiContent();
        const mallId = window.temuEarlyInterceptor.getCachedMallId();
        
        console.log('🔑 缓存的 anti-content:', antiContent ? antiContent.substring(0, 50) + '...' : 'null');
        console.log('🏪 缓存的 mallId:', mallId);
        
        return { antiContent, mallId };
    } else {
        console.log('❌ 早期拦截器未加载');
        return null;
    }
}

// 模拟一个真实的API请求来测试拦截
function simulateRealApiRequest() {
    console.log('📡 模拟真实的API请求...');
    
    // 使用您提供的真实数据
    const realAntiContent = '0aqAfa5e-wCEBxjHXbSt_USOOG7GyrnxdUOyHHcpmYtYghQpmYXtXJ7_NrQjTmxNqTGZhfp_KnGStKyCvw1mCM0h8cKfaykR0HbQB_nG9onYTJXpPon5P8npXjj0mjn0EanpXoXGdjnY9Pk64PX0nuDfse-vVkxvcpr3FuF3cTULKh1B_VSVBW-KA_7sxhbV_CEBcCez4H92maXI2d1YgoyswoG4Mf_ojlph0nGig8nYWzXYwTyh0j4W6jys6PxHjXvJslpHqXjseoYZhfYdqdnC_SXTs9Vl0gOH_Cp11v2PLPCnYtMPGyooOTxtkNCa4h4XObIGxoijmoIJmPIHrobaxxCX5PaoI_0kfdpvPUtc9d_YIFRpS6VBdr3vdDBtAvlWZ-wKwzlLDk8tY-JXdrhz2y1Abg296SjCEHO-t1yq';
    const realMallId = '634418223971228';
    
    console.log('🔑 使用真实的 anti-content:', realAntiContent.substring(0, 50) + '...');
    console.log('🏪 使用真实的 mallId:', realMallId);
    
    // 模拟fetch请求
    return fetch('https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount', {
        method: 'POST',
        headers: {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'anti-content': realAntiContent,
            'cache-control': 'max-age=0',
            'content-type': 'application/json',
            'mallid': realMallId,
            'origin': 'https://seller.kuajingmaihuo.com',
            'priority': 'u=1, i',
            'referer': 'https://seller.kuajingmaihuo.com/main/product/seller-select',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        },
        credentials: 'include',
        body: JSON.stringify({})
    }).then(response => {
        console.log('📡 API响应状态:', response.status);
        if (response.ok) {
            return response.json();
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    }).then(data => {
        console.log('✅ API响应数据:', data);
        return data;
    }).catch(error => {
        console.error('❌ API请求失败:', error);
        throw error;
    });
}

// 模拟XMLHttpRequest请求
function simulateXHRRequest() {
    console.log('📡 模拟XMLHttpRequest请求...');
    
    const realAntiContent = '0aqAfa5e-wCEBxjHXbSt_USOOG7GyrnxdUOyHHcpmYtYghQpmYXtXJ7_NrQjTmxNqTGZhfp_KnGStKyCvw1mCM0h8cKfaykR0HbQB_nG9onYTJXpPon5P8npXjj0mjn0EanpXoXGdjnY9Pk64PX0nuDfse-vVkxvcpr3FuF3cTULKh1B_VSVBW-KA_7sxhbV_CEBcCez4H92maXI2d1YgoyswoG4Mf_ojlph0nGig8nYWzXYwTyh0j4W6jys6PxHjXvJslpHqXjseoYZhfYdqdnC_SXTs9Vl0gOH_Cp11v2PLPCnYtMPGyooOTxtkNCa4h4XObIGxoijmoIJmPIHrobaxxCX5PaoI_0kfdpvPUtc9d_YIFRpS6VBdr3vdDBtAvlWZ-wKwzlLDk8tY-JXdrhz2y1Abg296SjCEHO-t1yq';
    const realMallId = '634418223971228';
    
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        xhr.open('POST', 'https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount');
        
        // 设置请求头（这些应该被拦截器捕获）
        xhr.setRequestHeader('accept', '*/*');
        xhr.setRequestHeader('accept-language', 'zh-CN,zh;q=0.9');
        xhr.setRequestHeader('anti-content', realAntiContent);
        xhr.setRequestHeader('cache-control', 'max-age=0');
        xhr.setRequestHeader('content-type', 'application/json');
        xhr.setRequestHeader('mallid', realMallId);
        xhr.setRequestHeader('origin', 'https://seller.kuajingmaihuo.com');
        
        xhr.onload = function() {
            if (xhr.status === 200) {
                console.log('✅ XHR响应成功:', xhr.responseText);
                resolve(JSON.parse(xhr.responseText));
            } else {
                console.error('❌ XHR响应失败:', xhr.status, xhr.statusText);
                reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
            }
        };
        
        xhr.onerror = function() {
            console.error('❌ XHR请求错误');
            reject(new Error('XHR请求错误'));
        };
        
        xhr.send(JSON.stringify({}));
    });
}

// 检查localStorage中的缓存
function checkLocalStorageCache() {
    console.log('💾 检查localStorage缓存...');
    
    const keys = [
        'temu_cached_anti_content',
        'temu_anti_content_expiry',
        'temu_cached_mall_id',
        'temu_mall_id_expiry'
    ];
    
    keys.forEach(key => {
        const value = localStorage.getItem(key);
        if (value) {
            if (key.includes('anti_content') && !key.includes('expiry')) {
                console.log(`📋 ${key}:`, value.substring(0, 50) + '...');
            } else if (key.includes('expiry')) {
                const timestamp = parseInt(value);
                const date = new Date(timestamp);
                const isValid = Date.now() < timestamp;
                console.log(`⏰ ${key}:`, date.toLocaleString(), isValid ? '✅有效' : '❌过期');
            } else {
                console.log(`📋 ${key}:`, value);
            }
        } else {
            console.log(`📋 ${key}:`, 'null');
        }
    });
}

// 运行完整测试
async function runFullTest() {
    console.log('🚀 开始完整测试...');
    console.log('='.repeat(60));
    
    // 1. 检查早期拦截器
    console.log('1️⃣ 检查早期拦截器状态');
    const interceptorResult = checkEarlyInterceptor();
    
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 2. 检查localStorage缓存
    console.log('2️⃣ 检查localStorage缓存');
    checkLocalStorageCache();
    
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 3. 模拟fetch请求
    console.log('3️⃣ 模拟fetch请求');
    try {
        await simulateRealApiRequest();
        console.log('✅ Fetch请求成功');
    } catch (error) {
        console.log('❌ Fetch请求失败:', error.message);
    }
    
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 4. 等待一下，然后检查缓存是否更新
    console.log('4️⃣ 检查缓存是否更新');
    setTimeout(() => {
        console.log('🔄 重新检查缓存状态...');
        checkEarlyInterceptor();
        checkLocalStorageCache();
    }, 1000);
    
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // 5. 模拟XHR请求
    console.log('5️⃣ 模拟XHR请求');
    try {
        await simulateXHRRequest();
        console.log('✅ XHR请求成功');
    } catch (error) {
        console.log('❌ XHR请求失败:', error.message);
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🏁 测试完成');
}

// 提供给用户的函数
window.testEarlyInterceptor = {
    runFullTest,
    checkEarlyInterceptor,
    simulateRealApiRequest,
    simulateXHRRequest,
    checkLocalStorageCache
};

console.log('🔧 早期拦截器测试工具已准备就绪！');
console.log('💡 使用方法：');
console.log('  - testEarlyInterceptor.runFullTest() - 运行完整测试');
console.log('  - testEarlyInterceptor.checkEarlyInterceptor() - 检查拦截器状态');
console.log('  - testEarlyInterceptor.simulateRealApiRequest() - 模拟真实API请求');
console.log('  - testEarlyInterceptor.checkLocalStorageCache() - 检查localStorage缓存');

// 自动运行一次检查
console.log('\n🚀 自动检查早期拦截器状态...');
checkEarlyInterceptor();
