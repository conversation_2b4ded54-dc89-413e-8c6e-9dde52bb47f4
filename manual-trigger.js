// 手动触发API请求的简化脚本
// 在Temu商家后台页面控制台运行

console.log('🎯 手动触发API请求脚本');

// 简单的手动触发函数
window.manualTrigger = {
    // 1. 检查当前缓存状态
    checkCache: function() {
        console.log('💾 检查当前缓存状态...');
        
        const caches = {
            ultimate: localStorage.getItem('ultimate_anti_content'),
            early: localStorage.getItem('temu_cached_anti_content'),
            optimized: localStorage.getItem('optimized_anti_content')
        };
        
        console.log('📋 缓存状态:', caches);
        
        const hasCache = caches.ultimate || caches.early || caches.optimized;
        if (hasCache) {
            console.log('✅ 已有缓存数据');
            return true;
        } else {
            console.log('❌ 没有缓存数据');
            return false;
        }
    },

    // 2. 尝试触发待办事项API
    triggerTodoApi: async function() {
        console.log('📡 触发待办事项API...');
        
        try {
            const response = await fetch('https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount', {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'content-type': 'application/json',
                    'origin': 'https://seller.kuajingmaihuo.com',
                    'referer': window.location.href
                },
                credentials: 'include',
                body: '{}'
            });
            
            console.log('📡 待办事项API响应:', response.status);
            
            if (response.status === 403) {
                console.log('⚠️ 403错误是预期的（缺少anti-content），但应该触发了拦截器');
            }
            
            return response.status;
        } catch (error) {
            console.log('❌ 待办事项API失败:', error);
            return null;
        }
    },

    // 3. 点击页面中的刷新按钮
    clickRefreshButtons: function() {
        console.log('🖱️ 查找并点击刷新按钮...');
        
        const selectors = [
            'button[class*="refresh"]',
            'button[title*="刷新"]',
            'button[title*="refresh"]',
            '.ant-btn-icon-only',
            '[aria-label*="刷新"]'
        ];
        
        let clicked = 0;
        
        selectors.forEach(selector => {
            const buttons = document.querySelectorAll(selector);
            console.log(`🔍 找到 ${selector}:`, buttons.length);
            
            buttons.forEach((button, index) => {
                if (index < 2 && clicked < 3) {
                    try {
                        console.log('🖱️ 点击按钮:', button);
                        button.click();
                        clicked++;
                    } catch (error) {
                        console.warn('点击失败:', error);
                    }
                }
            });
        });
        
        console.log(`✅ 共点击了 ${clicked} 个按钮`);
        return clicked;
    },

    // 4. 等待并检查结果
    waitAndCheck: async function(seconds = 10) {
        console.log(`⏰ 等待 ${seconds} 秒后检查结果...`);
        
        for (let i = 0; i < seconds; i++) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (i % 3 === 0) { // 每3秒检查一次
                const hasCache = this.checkCache();
                if (hasCache) {
                    console.log('🎉 检测到缓存数据！');
                    return true;
                }
            }
        }
        
        console.log('⏰ 等待结束');
        return this.checkCache();
    },

    // 5. 完整的手动触发流程
    run: async function() {
        console.log('🚀 开始手动触发流程...');
        
        // 1. 检查初始状态
        console.log('\n1️⃣ 检查初始状态');
        const initialCache = this.checkCache();
        
        if (initialCache) {
            console.log('✅ 已有缓存，无需触发');
            return true;
        }
        
        // 2. 点击刷新按钮
        console.log('\n2️⃣ 点击页面按钮');
        this.clickRefreshButtons();
        
        // 3. 等待3秒
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 4. 触发API
        console.log('\n3️⃣ 触发API请求');
        await this.triggerTodoApi();
        
        // 5. 等待并检查结果
        console.log('\n4️⃣ 等待结果');
        const result = await this.waitAndCheck(15);
        
        if (result) {
            console.log('🎉 成功获取到anti-content数据！');
            console.log('💡 现在可以测试扩展功能了');
        } else {
            console.log('❌ 未能获取到数据');
            console.log('💡 建议：');
            console.log('  1. 手动在页面中进行一些操作（点击菜单、查看商品等）');
            console.log('  2. 等待页面自动刷新数据');
            console.log('  3. 重新运行 manualTrigger.run()');
        }
        
        return result;
    }
};

console.log('🔧 手动触发工具已准备就绪！');
console.log('💡 运行 manualTrigger.run() 开始手动触发');
console.log('💡 运行 manualTrigger.checkCache() 检查缓存');
console.log('💡 运行 manualTrigger.clickRefreshButtons() 点击刷新按钮');

// 提示用户
console.log('\n📝 使用说明：');
console.log('1. 确保你在 Temu 商家后台页面');
console.log('2. 运行 manualTrigger.run() 开始自动触发');
console.log('3. 或者手动在页面中进行操作（推荐）：');
console.log('   - 点击左侧菜单的不同选项');
console.log('   - 查看商品列表');
console.log('   - 查看订单列表');
console.log('   - 等待页面数据自动刷新');
console.log('4. 然后运行 manualTrigger.checkCache() 检查是否获取到数据');

// 自动检查一次
manualTrigger.checkCache();
