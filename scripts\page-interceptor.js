// ================================================================
//  页面环境拦截器 (page-interceptor.js)
//  此文件将通过 web_accessible_resources 注入页面
// ================================================================

(function() {
  // 防止重复注入
  if (window.hasTemuInterceptorInjected) {
    return;
  }
  window.hasTemuInterceptorInjected = true;

  console.log('🚀 [Page-Interceptor] 脚本已注入页面主环境，开始设置拦截器...');

  const storeToLocalStorage = (key, value, expiryMinutes) => {
    try {
      const expiry = Date.now() + expiryMinutes * 60 * 1000;
      localStorage.setItem(`temu_cs_${key}`, value);
      localStorage.setItem(`temu_cs_${key}_expiry`, expiry.toString());
    } catch (e) {
      console.warn(`[Page-Interceptor] 保存 ${key} 到 localStorage 失败:`, e);
    }
  };

  const checkAndStoreHeaders = (headers, source) => {
    const lowerCaseHeaders = {};
    for (const key in headers) {
      lowerCaseHeaders[key.toLowerCase()] = headers[key];
    }
    
    const antiContent = lowerCaseHeaders['anti-content'];
    const mallId = lowerCaseHeaders['mallid'];

    if (antiContent) {
      console.log(`🎉 [Page-Interceptor] 捕获到 anti-content (来自 ${source})!`);
      storeToLocalStorage('anti_content', antiContent, 30); // 30分钟有效期
    }

    if (mallId) {
      console.log(`🎉 [Page-Interceptor] 捕获到 mallId (来自 ${source})!`);
      storeToLocalStorage('mall_id', mallId, 60); // 60分钟有效期
    }
  };

  // 1. 拦截 fetch
  const originalFetch = window.fetch;
  window.fetch = function (...args) {
    const [url, options] = args;
    const urlString = typeof url === 'string' ? url : url.toString();

    if (urlString.includes('seller.kuajingmaihuo.com') || urlString.includes('.temu.com/')) {
      if (options && options.headers) {
        const headersObj = {};
        if (options.headers instanceof Headers) {
          options.headers.forEach((value, key) => {
            headersObj[key] = value;
          });
        } else {
          Object.assign(headersObj, options.headers);
        }
        checkAndStoreHeaders(headersObj, 'fetch');
      }
    }
    return originalFetch.apply(this, args);
  };

  // 2. 拦截 XHR
  const originalXHRSetHeader = XMLHttpRequest.prototype.setRequestHeader;
  XMLHttpRequest.prototype.setRequestHeader = function (name, value) {
    const lowerName = name.toLowerCase();
    // 这里的拦截没有 URL 上下文，但对于 Temu 来说足够了
    if (lowerName === 'anti-content' || lowerName === 'mallid') {
        checkAndStoreHeaders({ [name]: value }, 'XHR-SetHeader');
    }
    return originalXHRSetHeader.call(this, name, value);
  };
  
  console.log('✅ [Page-Interceptor] 所有拦截器设置完毕。');
})();