<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useShopBinding } from '../../../composables/useShopBinding'
import { useNotification } from '../../../composables/useNotification'
import dianxiaomiDetectionService from '../../../services/dianxiaomiDetectionService'
import type { ShopAccount, FreightTemplate, ProductCategory } from '../../../services/dianxiaomiDetectionService'
import CategoryCascader from '../components/CategoryCascader.vue'
import { getSiteOptions, getSiteById, type TemuSite } from '../../../config/temuSites'

const { temuSiteInfo } = useShopBinding()
const { success, error: showError, warning, info } = useNotification()

const activeStep = ref(0)

const steps = ref([
  {
    key: 'basic',
    title: '基础设置',
    icon: '☁️',
    description: '配置ERP平台、店铺信息等基础设置'
  },
  {
    key: 'config',
    title: '上品配置',
    icon: '⚙️',
    description: '配置库存、价格、标题等上品参数'
  },
  {
    key: 'collection',
    title: '货盘采集',
    icon: '🌐',
    description: '配置货盘采集规则和数据源'
  }
])

// 店小秘登录状态
const dianxiaomiLoginStatus = ref({
  isLoggedIn: false,
  message: '',
  loading: false
})

// 数据状态
const shopAccounts = ref<ShopAccount[]>([])
const warehouses = ref<Record<string, Record<string, Record<string, string>>>>({})
const freightTemplates = ref<FreightTemplate[]>([])
const productCategories = ref<ProductCategory[]>([])

// 加载状态
const loadingStates = ref({
  shopAccounts: false,
  warehouses: false,
  freightTemplates: false,
  productCategories: false
})

const basicForm = ref({
  erpPlatform: '店小秘',
  publishSite: 'Temu',
  shopAccount: '',
  publishStatus: '2', // 1: 草稿箱, 2: 直接发布, 3: 待发布
  businessSite: '100', // 默认美国站，使用站点ID
  warehouse: '',
  freightTemplate: '',
  shippingTime: '172800', // 发货时效(秒)
  venue: '',
  productCategory: '',
  productAttributes: ''
})

// 站点选项
const siteOptions = computed(() => getSiteOptions())

// 计算属性
const currentTemuShop = computed(() => {
  if (temuSiteInfo.value) {
    return `${temuSiteInfo.value.mallName} (${temuSiteInfo.value.isSemiManagedMall ? '半托' : '全托'})`
  }
  return 'Temu'
})

// 检查授权是否即将过期（30天内）
const isExpiringSoon = (expireTime: string): boolean => {
  if (!expireTime) return false
  try {
    const expireDate = new Date(expireTime)
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 30 && diffDays > 0
  } catch {
    return false
  }
}

// 检测店小秘登录状态
const checkDianxiaomiLogin = async () => {
  dianxiaomiLoginStatus.value.loading = true
  try {
    const result = await dianxiaomiDetectionService.checkLoginStatus()
    dianxiaomiLoginStatus.value = {
      isLoggedIn: result.isLoggedIn,
      message: result.message,
      loading: false
    }

    if (result.isLoggedIn) {
      // 如果已登录，自动获取数据
      await Promise.all([
        loadShopAccounts(),
        loadWarehouses(),
        loadFreightTemplates(),
        loadProductCategories()
      ])
    }
  } catch (error) {
    console.error('[ProductCenter] 检测店小秘登录失败:', error)
    dianxiaomiLoginStatus.value = {
      isLoggedIn: false,
      message: '检测失败，请重试',
      loading: false
    }
  }
}

// 打开店小秘ERP
const openDianxiaomiERP = () => {
  window.open('https://www.dianxiaomi.com/index.htm', '_blank')
}

// 加载店铺账号
const loadShopAccounts = async () => {
  console.info('[ProductCenter] 开始加载店铺账号...')
  loadingStates.value.shopAccounts = true
  try {
    console.info('[ProductCenter] 调用 dianxiaomiDetectionService.getShopAccounts()')
    const result = await dianxiaomiDetectionService.getShopAccounts()
    console.info('[ProductCenter] 店铺账号API返回结果:', result)

    if (result.success && result.data) {
      shopAccounts.value = result.data
      console.info('[ProductCenter] 成功设置店铺账号数据:', result.data)

      // 优先使用session中保存的选中店铺，否则选择第一个
      const selectedShopId = await dianxiaomiDetectionService.getSelectedShopId()
      if (selectedShopId && result.data.some(shop => shop.shopId === selectedShopId)) {
        basicForm.value.shopAccount = selectedShopId
        console.info('[ProductCenter] 使用session中的选中店铺:', selectedShopId)
      } else if (result.data.length > 0 && !basicForm.value.shopAccount) {
        basicForm.value.shopAccount = result.data[0].shopId
        console.info('[ProductCenter] 默认选择第一个店铺:', result.data[0])
        // 保存到session
        await dianxiaomiDetectionService.setSelectedShopId(result.data[0].shopId)
      }

      // 显示成功提示
      success('店铺账号同步成功', `成功获取 ${result.data.length} 个店铺账号`)
    } else {
      console.error('[ProductCenter] 获取店铺账号失败:', result.error)
      showError('获取店铺账号失败', result.error || '未知错误')
    }
  } catch (error) {
    console.error('[ProductCenter] 加载店铺账号异常:', error)
    showError('加载店铺账号失败', error instanceof Error ? error.message : '未知错误')
  } finally {
    loadingStates.value.shopAccounts = false
    console.info('[ProductCenter] 店铺账号加载完成')
  }
}

// 加载发货仓库
const loadWarehouses = async () => {
  loadingStates.value.warehouses = true
  try {
    const result = await dianxiaomiDetectionService.getWarehouses()
    if (result.success && result.data) {
      warehouses.value = result.data

      // 计算仓库总数
      let warehouseCount = 0
      Object.values(result.data).forEach(shopWarehouses => {
        Object.values(shopWarehouses).forEach(siteWarehouses => {
          warehouseCount += Object.keys(siteWarehouses).length
        })
      })

      // 显示成功提示
      success('发货仓库同步成功', `成功获取 ${warehouseCount} 个发货仓库`)
    } else {
      showError('获取发货仓库失败', result.error || '未知错误')
    }
  } catch (error) {
    console.error('[ProductCenter] 加载发货仓库失败:', error)
    showError('加载发货仓库失败', error instanceof Error ? error.message : '未知错误')
  } finally {
    loadingStates.value.warehouses = false
  }
}

// 加载运费模板
const loadFreightTemplates = async () => {
  loadingStates.value.freightTemplates = true
  try {
    const result = await dianxiaomiDetectionService.getFreightTemplates()
    if (result.success && result.data) {
      freightTemplates.value = result.data
      // 如果有运费模板，默认选择第一个
      if (result.data.length > 0 && !basicForm.value.freightTemplate) {
        basicForm.value.freightTemplate = result.data[0].freightTemplateId
      }

      // 显示成功提示
      success('运费模板同步成功', `成功获取 ${result.data.length} 个运费模板`)
    } else {
      showError('获取运费模板失败', result.error || '未知错误')
    }
  } catch (error) {
    console.error('[ProductCenter] 加载运费模板失败:', error)
    showError('加载运费模板失败', error instanceof Error ? error.message : '未知错误')
  } finally {
    loadingStates.value.freightTemplates = false
  }
}

// 加载商品分类
const loadProductCategories = async () => {
  loadingStates.value.productCategories = true
  try {
    const result = await dianxiaomiDetectionService.getProductCategories()
    if (result.success && result.data) {
      productCategories.value = result.data
    } else {
      showError('获取商品分类失败', result.error || '未知错误')
    }
  } catch (error) {
    console.error('[ProductCenter] 加载商品分类失败:', error)
    showError('加载商品分类失败', error instanceof Error ? error.message : '未知错误')
  } finally {
    loadingStates.value.productCategories = false
  }
}

// 获取仓库选项 - 根据新的API响应格式
const getWarehouseOptions = computed(() => {
  const options: Array<{ value: string; label: string; shopId: string; site: string }> = []

  // 新的API响应格式: { "6959965": { "100": { "WH-06912611061892972": "亚马逊" } } }
  Object.entries(warehouses.value).forEach(([shopId, shopWarehouses]) => {
    Object.entries(shopWarehouses).forEach(([site, siteWarehouses]) => {
      Object.entries(siteWarehouses).forEach(([warehouseId, warehouseName]) => {
        options.push({
          value: warehouseId,
          label: `${warehouseName} (店铺: ${shopId}, 站点: ${site})`,
          shopId,
          site
        })
      })
    })
  })

  return options
})

const switchStep = (stepIndex: number) => {
  activeStep.value = stepIndex
}

// 分类选择变化处理
const onCategoryChange = (category: ProductCategory | null) => {
  console.info('[ProductCenter] 分类选择变化:', category)
  if (category) {
    // 可以在这里处理分类选择后的逻辑，比如加载对应的属性等
    console.info('[ProductCenter] 选择的分类:', {
      id: category.id,
      catId: category.catId,
      catName: category.catName,
      catLevel: category.catLevel
    })
  }
}

// 动态加载分类数据
const loadCategoriesByParent = async (parentId?: number): Promise<ProductCategory[]> => {
  try {
    console.info('[ProductCenter] 动态加载分类数据，父ID:', parentId)

    // 向content script发送消息，请求加载特定父分类的子分类
    const dianxiaomiTab = await chrome.tabs.query({ url: '*://www.dianxiaomi.com/*' })
    if (dianxiaomiTab.length === 0) {
      throw new Error('未找到店小秘标签页')
    }

    const response = await chrome.tabs.sendMessage(dianxiaomiTab[0].id!, {
      action: 'GET_PRODUCT_CATEGORIES',
      parentId: parentId
    })

    if (response && response.success) {
      console.info('[ProductCenter] 动态加载分类成功:', response.data)
      return response.data || []
    } else {
      throw new Error(response?.error || '加载分类失败')
    }
  } catch (error) {
    console.error('[ProductCenter] 动态加载分类失败:', error)
    showError('加载分类失败', error instanceof Error ? error.message : '未知错误')
    return []
  }
}

const saveBasicSettings = () => {
  console.log('保存基础设置:', basicForm.value)
  success('基础设置保存成功！')
}

// 上品配置表单
const configForm = ref({
  minStock: 12,
  fixedStock: null,
  enableDeduplication: true,
  titlePrefix: '',
  titleSuffix: '',
  uploadInterval: 0,
  priceMultiplier: 4,
  collectDetails: ['title', 'img'],
  collectSku: true,
  externalLink: false,
  defaultSize: {
    length: 10,
    width: 10,
    height: 10,
    weight: 20
  },
  filterProhibited: true,
  prohibitedWords: '',
  enableTranslation: false,
  translationService: 'google'
})

const saveConfig = () => {
  console.log('保存上品配置:', configForm.value)
  success('上品配置保存成功！')
}

// 监听店铺选择变化
const onShopAccountChange = async (shopId: string) => {
  if (shopId) {
    console.info('[ProductCenter] 店铺选择变化:', shopId)
    await dianxiaomiDetectionService.setSelectedShopId(shopId)

    // 重新加载依赖店铺的数据
    await Promise.all([
      loadWarehouses(),
      loadFreightTemplates()
    ])
  }
}

// 初始化
onMounted(() => {
  checkDianxiaomiLogin()
})

// 货盘采集表单
const collectionForm = ref({
  dataSource: 'temu',
  collectionUrl: '',
  autoCollection: false,
  collectionInterval: 60,
  maxItems: 100,
  filterRules: '',
  keywords: '',
  priceRange: {
    min: null,
    max: null
  },
  categoryFilter: '',
  ratingFilter: 4,
  salesFilter: 100,
  enableProxy: false,
  proxyConfig: '',
  collectImages: true,
  collectReviews: false,
  maxReviews: 50
})

// 开始货盘采集
const startCollection = () => {
  console.log('开始货盘采集:', collectionForm.value)
  alert('货盘采集已启动！')
}
</script>

<template>
  <div class="h-full flex flex-col">
    <!-- 步骤导航 - 固定高度 -->
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6 flex-shrink-0">
      <div class="flex items-center justify-between">
        <div v-for="(step, index) in steps" :key="step.key" class="flex items-center">
          <!-- 步骤圆圈 -->
          <div
            @click="switchStep(index)"
            :class="[
              'flex items-center justify-center w-12 h-12 rounded-full border-2 cursor-pointer transition-colors',
              activeStep === index
                ? 'bg-blue-500 border-blue-500 text-white'
                : activeStep > index
                  ? 'bg-green-500 border-green-500 text-white'
                  : 'bg-gray-100 border-gray-300 text-gray-500'
            ]"
          >
            <span class="text-lg">{{ step.icon }}</span>
          </div>

          <!-- 步骤信息 -->
          <div class="ml-4">
            <div
              :class="[
                'font-medium',
                activeStep === index ? 'text-blue-600' : 'text-gray-700'
              ]"
            >
              {{ step.title }}
            </div>
            <div class="text-sm text-gray-500">{{ step.description }}</div>
          </div>

          <!-- 连接线 -->
          <div
            v-if="index < steps.length - 1"
            :class="[
              'flex-1 h-0.5 mx-8',
              activeStep > index ? 'bg-green-500' : 'bg-gray-300'
            ]"
          ></div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 - 占满剩余空间 -->
    <div class="flex-1 bg-white border border-gray-200 rounded-lg overflow-hidden">
      <div class="h-full overflow-y-auto p-6">
        <div v-if="activeStep === 0">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">基础设置</h3>
        <form @submit.prevent="saveBasicSettings" class="space-y-6">
          <!-- ERP平台和发布站点 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">ERP平台 *</label>
              <div class="relative">
                <select v-model="basicForm.erpPlatform" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                  <option value="店小秘">店小秘</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center pr-8 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">发布站点 *</label>
              <div class="relative">
                <div class="w-full px-3 py-2 border border-gray-300 rounded bg-gray-50 flex items-center">
                  <span v-if="temuSiteInfo?.isSemiManagedMall" class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                    半托
                  </span>
                  <span v-else class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 mr-2">
                    全托
                  </span>
                  <span>{{ currentTemuShop }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 店小秘登录状态检测 -->
          <div v-if="!dianxiaomiLoginStatus.isLoggedIn" class="border border-red-200 rounded-lg p-4 bg-red-50">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-red-800">{{ dianxiaomiLoginStatus.message }}</span>
              </div>
              <div class="flex space-x-2">
                <button
                  @click="openDianxiaomiERP"
                  type="button"
                  class="px-4 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                >
                  打开店小秘ERP
                </button>
                <button
                  @click="checkDianxiaomiLogin"
                  :disabled="dianxiaomiLoginStatus.loading"
                  type="button"
                  class="px-4 py-2 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 disabled:opacity-50"
                >
                  {{ dianxiaomiLoginStatus.loading ? '检测中...' : '再次重试' }}
                </button>
              </div>
            </div>
          </div>

          <!-- 店铺账号 -->
          <div v-if="dianxiaomiLoginStatus.isLoggedIn">
            <div class="flex items-center justify-between mb-2">
              <label class="block text-sm font-medium text-gray-700">店铺账号 *</label>
              <span v-if="shopAccounts.length > 0" class="text-xs text-gray-500">
                共 {{ shopAccounts.length }} 个店铺
              </span>
            </div>
            <div class="flex items-center space-x-4">
              <div class="flex-1 relative">
                <select
                  v-model="basicForm.shopAccount"
                  @change="onShopAccountChange(basicForm.shopAccount)"
                  class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  :disabled="loadingStates.shopAccounts"
                >
                  <option value="">请选择店铺账号</option>
                  <option v-for="shop in shopAccounts" :key="shop.shopId" :value="shop.shopId">
                    {{ shop.shopName }} (ID: {{ shop.shopId }}) - {{ shop.currency }}
                  </option>
                </select>
                <div v-if="loadingStates.shopAccounts" class="absolute inset-y-0 right-8 flex items-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </div>
              </div>
              <button
                @click="loadShopAccounts"
                :disabled="loadingStates.shopAccounts"
                type="button"
                class="px-4 py-2 text-blue-600 hover:text-blue-800 disabled:opacity-50"
              >
                {{ loadingStates.shopAccounts ? '同步中...' : '同步' }}
              </button>
            </div>

            <!-- 显示选中店铺的详细信息 -->
            <div v-if="basicForm.shopAccount" class="mt-3">
              <div v-for="shop in shopAccounts" :key="shop.shopId">
                <div v-if="shop.shopId === basicForm.shopAccount" class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="text-sm font-medium text-blue-900">{{ shop.shopName }}</h4>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      已绑定
                    </span>
                  </div>
                  <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div class="flex items-center">
                      <span class="text-gray-500 w-16">店铺ID:</span>
                      <span class="font-mono">{{ shop.shopId }}</span>
                    </div>
                    <div class="flex items-center">
                      <span class="text-gray-500 w-16">币种:</span>
                      <span class="font-medium text-blue-700">{{ shop.currency }}</span>
                    </div>
                    <div v-if="shop.authTime" class="flex items-center col-span-2">
                      <span class="text-gray-500 w-16">授权:</span>
                      <span>{{ shop.authTime }}</span>
                    </div>
                    <div v-if="shop.expireTime" class="flex items-center col-span-2">
                      <span class="text-gray-500 w-16">过期:</span>
                      <span :class="isExpiringSoon(shop.expireTime) ? 'text-red-600 font-medium' : ''">
                        {{ shop.expireTime }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 发布状态 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">发布状态</label>
            <div class="flex space-x-6">
              <label class="flex items-center">
                <input
                  v-model="basicForm.publishStatus"
                  type="radio"
                  value="2"
                  class="mr-2"
                >
                <span>直接发布</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="basicForm.publishStatus"
                  type="radio"
                  value="3"
                  class="mr-2"
                >
                <span>移入DXM待发布</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="basicForm.publishStatus"
                  type="radio"
                  value="1"
                  class="mr-2"
                >
                <span>放置DXM草稿箱</span>
              </label>
            </div>
          </div>

          <!-- 经营站点 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">经营站点 *</label>
            <div class="space-y-2">
              <select
                v-model="basicForm.businessSite"
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">请选择经营站点</option>
                <option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }} ({{ site.code }})
                </option>
              </select>

              <!-- 显示选中站点的详细信息 -->
              <div v-if="basicForm.businessSite" class="text-sm text-gray-600">
                <div v-if="getSiteById(Number(basicForm.businessSite))" class="flex items-center space-x-4">
                  <span class="text-blue-700">
                    {{ getSiteById(Number(basicForm.businessSite))?.region }}
                  </span>
                  <span class="text-gray-500">
                    货币：{{ getSiteById(Number(basicForm.businessSite))?.currency }}
                  </span>
                  <span class="text-gray-500">
                    语言：{{ getSiteById(Number(basicForm.businessSite))?.language }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 发货仓库 -->
          <div v-if="dianxiaomiLoginStatus.isLoggedIn">
            <label class="block text-sm font-medium text-gray-700 mb-2">发货仓库 *</label>
            <div class="flex items-center space-x-4">
              <div class="flex-1 relative">
                <select
                  v-model="basicForm.warehouse"
                  class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  :disabled="loadingStates.warehouses"
                >
                  <option value="">请选择发货仓库</option>
                  <option v-for="warehouse in getWarehouseOptions" :key="warehouse.value" :value="warehouse.value">
                    {{ warehouse.label }}
                  </option>
                </select>
                <div v-if="loadingStates.warehouses" class="absolute inset-y-0 right-8 flex items-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </div>
              </div>
              <button
                @click="loadWarehouses"
                :disabled="loadingStates.warehouses"
                type="button"
                class="px-4 py-2 text-blue-600 hover:text-blue-800 disabled:opacity-50"
              >
                {{ loadingStates.warehouses ? '同步中...' : '同步' }}
              </button>
            </div>
          </div>

          <!-- 运费模板 -->
          <div v-if="dianxiaomiLoginStatus.isLoggedIn">
            <label class="block text-sm font-medium text-gray-700 mb-2">运费模板 *</label>
            <div class="flex items-center space-x-4">
              <div class="flex-1 relative">
                <select
                  v-model="basicForm.freightTemplate"
                  class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  :disabled="loadingStates.freightTemplates"
                >
                  <option value="">请选择运费模板</option>
                  <option v-for="template in freightTemplates" :key="template.id" :value="template.freightTemplateId">
                    {{ template.templateName }} (店铺: {{ template.shopId }}, 站点: {{ template.site }})
                  </option>
                </select>
                <div v-if="loadingStates.freightTemplates" class="absolute inset-y-0 right-8 flex items-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </div>
              </div>
              <button
                @click="loadFreightTemplates"
                :disabled="loadingStates.freightTemplates"
                type="button"
                class="px-4 py-2 text-blue-600 hover:text-blue-800 disabled:opacity-50"
              >
                {{ loadingStates.freightTemplates ? '同步中...' : '同步' }}
              </button>
            </div>
          </div>

          <!-- 发货时效 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">发货时效 *</label>
            <div class="flex space-x-6">
              <label class="flex items-center">
                <input
                  v-model="basicForm.shippingTime"
                  type="radio"
                  value="86400"
                  class="mr-2"
                >
                <span>1个工作日内发货</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="basicForm.shippingTime"
                  type="radio"
                  value="172800"
                  class="mr-2"
                >
                <span>2个工作日内发货</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="basicForm.shippingTime"
                  type="radio"
                  value="777600"
                  class="mr-2"
                >
                <span>9个工作日内发货(Y2)</span>
              </label>
            </div>
          </div>

          <!-- 商品分类 -->
          <div v-if="dianxiaomiLoginStatus.isLoggedIn">
            <label class="block text-sm font-medium text-gray-700 mb-2">商品分类 *</label>
            <div class="flex items-center space-x-4">
              <div class="flex-1">
                <CategoryCascader
                  v-model="basicForm.productCategory"
                  :loading="loadingStates.productCategories"
                  placeholder="请选择商品分类"
                  @change="onCategoryChange"
                  @load-categories="loadCategoriesByParent"
                />
              </div>
              <button
                @click="loadProductCategories"
                :disabled="loadingStates.productCategories"
                type="button"
                class="px-4 py-2 text-blue-600 hover:text-blue-800 disabled:opacity-50"
              >
                {{ loadingStates.productCategories ? '同步中...' : '同步' }}
              </button>
            </div>
          </div>

          <!-- 产品属性 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">产品属性</label>
            <button
              type="button"
              class="text-blue-600 hover:text-blue-800 underline"
            >
              自定义跳转
            </button>
          </div>
          <div class="flex justify-center pt-6">
            <button type="submit" class="px-8 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">保存信息</button>
          </div>
        </form>
      </div>
      <!-- 上品配置 -->
      <div v-else-if="activeStep === 1">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">上品配置</h3>
        <form @submit.prevent="saveConfig" class="space-y-6">
          <!-- 最小库存 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">最小库存 *</label>
            <div class="flex items-center space-x-2">
              <input
                v-model.number="configForm.minStock"
                type="number"
                min="0"
                class="w-32 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
              <span class="text-sm text-gray-600">件</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">货盘库存低于配置最小库存，将不发布上品</div>
          </div>

          <!-- 固定上品库存 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">固定上品库存</label>
            <div class="flex items-center space-x-2">
              <input
                v-model.number="configForm.fixedStock"
                type="number"
                min="0"
                placeholder="不设置则为空"
                class="w-32 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <span class="text-sm text-gray-600">件</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>设置固定后，将以设置的库存数为上品库存。</li>
                <li>注意：更改保存后，请刷新货盘列表，否则数据不会生效。</li>
              </ul>
            </div>
          </div>

          <!-- 是否判断去重 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">是否判断去重</label>
            <div class="flex items-center space-x-4">
              <label class="flex items-center cursor-pointer">
                <input
                  v-model="configForm.enableDeduplication"
                  type="checkbox"
                  class="mr-2"
                >
                <span class="text-sm">{{ configForm.enableDeduplication ? '使用' : '关闭' }}</span>
              </label>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>如果启用，则上品将使用货盘的去重规则。</li>
                <li>去重规则为：同个店铺同个货盘,如上次已上过品，则本次将不执行上品。</li>
                <li>注意：更改保存后，请刷新货盘列表，否则数据不会生效。</li>
              </ul>
            </div>
          </div>

          <!-- 标题前后缀 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">标题前后缀</label>
            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600 w-16">标题前缀</span>
                <input
                  v-model="configForm.titlePrefix"
                  type="text"
                  maxlength="100"
                  placeholder="请输入关键词(选填)"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <span class="text-xs text-gray-500">{{ configForm.titlePrefix.length }} / 100</span>
              </div>
              <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-600 w-16">标题后缀</span>
                <input
                  v-model="configForm.titleSuffix"
                  type="text"
                  maxlength="170"
                  placeholder="请输入关键词(选填)"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <span class="text-xs text-gray-500">{{ configForm.titleSuffix.length }} / 170</span>
              </div>
            </div>
            <div class="mt-1 text-xs text-gray-500">选填，标题前缀和标题后缀，将分别添加到标题前面和后面。</div>
          </div>

          <!-- 上品时间间隔 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">上品时间间隔</label>
            <div class="flex items-center space-x-2">
              <input
                v-model.number="configForm.uploadInterval"
                type="number"
                min="0"
                class="w-32 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <span class="text-sm text-gray-600">s</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>一般默认为0</li>
                <li>注释：上完一个品后，下个品的时间间隔。</li>
              </ul>
            </div>
          </div>

          <!-- 上品价格上浮 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">上品价格上浮 *</label>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-600">默认</span>
              <input
                v-model.number="configForm.priceMultiplier"
                type="number"
                min="1"
                step="0.1"
                class="w-32 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
              <span class="text-sm text-gray-600">倍</span>
            </div>
          </div>

          <!-- 采集详情需要 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">采集详情需要</label>
            <div class="flex space-x-6">
              <label class="flex items-center cursor-pointer">
                <input
                  v-model="configForm.collectDetails"
                  type="checkbox"
                  value="title"
                  class="mr-2"
                >
                <span class="text-sm">文字</span>
              </label>
              <label class="flex items-center cursor-pointer">
                <input
                  v-model="configForm.collectDetails"
                  type="checkbox"
                  value="img"
                  class="mr-2"
                >
                <span class="text-sm">详情图</span>
              </label>
            </div>
          </div>

          <!-- 采集SKU货号 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">采集SKU货号</label>
            <div class="flex items-center space-x-4">
              <label class="flex items-center cursor-pointer">
                <input
                  v-model="configForm.collectSku"
                  type="checkbox"
                  class="mr-2"
                >
                <span class="text-sm">{{ configForm.collectSku ? '使用' : '关闭' }}</span>
              </label>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside">
                <li>使用时，上品将会默认自动把商品采集的货号填入SKU货号中</li>
              </ul>
            </div>
          </div>

          <!-- 站外产品链接 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">站外产品链接</label>
            <div class="flex items-center space-x-4">
              <label class="flex items-center cursor-pointer">
                <input
                  v-model="configForm.externalLink"
                  type="checkbox"
                  class="mr-2"
                >
                <span class="text-sm">{{ configForm.externalLink ? '使用' : '关闭' }}</span>
              </label>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>使用时，上品将会默认自动把商品采集的链接填入：站外产品链接</li>
                <li>仅对TEMU采集模式有效</li>
              </ul>
            </div>
          </div>

          <!-- 默认尺寸 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">默认尺寸</label>
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600 w-12">长边</span>
                  <input
                    v-model.number="configForm.defaultSize.length"
                    type="number"
                    min="0"
                    class="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                  <span class="text-sm text-gray-600">cm</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600 w-12">次长</span>
                  <input
                    v-model.number="configForm.defaultSize.width"
                    type="number"
                    min="0"
                    class="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                  <span class="text-sm text-gray-600">cm</span>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600 w-12">短边</span>
                  <input
                    v-model.number="configForm.defaultSize.height"
                    type="number"
                    min="0"
                    class="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                  <span class="text-sm text-gray-600">cm</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600 w-12">重量</span>
                  <input
                    v-model.number="configForm.defaultSize.weight"
                    type="number"
                    min="0"
                    class="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                  <span class="text-sm text-gray-600">g</span>
                </div>
              </div>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside">
                <li>如果商品尺寸为空时，将会使用默认尺寸。</li>
              </ul>
            </div>
          </div>

          <!-- 违禁词过滤 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">违禁词过滤</label>
            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <label class="flex items-center cursor-pointer">
                  <input
                    v-model="configForm.filterProhibited"
                    type="checkbox"
                    class="mr-2"
                  >
                  <span class="text-sm">{{ configForm.filterProhibited ? '使用' : '关闭' }}</span>
                </label>
              </div>
              <div v-if="configForm.filterProhibited">
                <textarea
                  v-model="configForm.prohibitedWords"
                  rows="4"
                  placeholder="请输入违禁词，每行一个..."
                  class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>启用后，系统将自动过滤包含违禁词的商品标题和描述。</li>
                <li>每行输入一个违禁词，支持模糊匹配。</li>
              </ul>
            </div>
          </div>

          <!-- 翻译设置 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">自动翻译</label>
            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <label class="flex items-center cursor-pointer">
                  <input
                    v-model="configForm.enableTranslation"
                    type="checkbox"
                    class="mr-2"
                  >
                  <span class="text-sm">{{ configForm.enableTranslation ? '启用' : '关闭' }}</span>
                </label>
              </div>
              <div v-if="configForm.enableTranslation" class="flex items-center space-x-4">
                <span class="text-sm text-gray-600">翻译服务</span>
                <select
                  v-model="configForm.translationService"
                  class="px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="google">Google 翻译</option>
                  <option value="baidu">百度翻译</option>
                  <option value="youdao">有道翻译</option>
                </select>
              </div>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>启用后，系统将自动翻译商品标题和描述到目标语言。</li>
                <li>翻译质量取决于所选择的翻译服务。</li>
              </ul>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div class="flex justify-center pt-6">
            <button
              type="submit"
              class="px-8 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              保存配置
            </button>
          </div>
        </form>
      </div>
      <!-- 货盘采集 -->
      <div v-else-if="activeStep === 2">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">货盘采集</h3>
        <form @submit.prevent="startCollection" class="space-y-6">
          <!-- 数据源选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">数据源 *</label>
            <select
              v-model="collectionForm.dataSource"
              class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="temu">Temu</option>
              <option value="amazon">Amazon</option>
              <option value="aliexpress">AliExpress</option>
              <option value="1688">1688</option>
              <option value="taobao">淘宝</option>
            </select>
          </div>

          <!-- 采集链接 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">采集链接</label>
            <textarea
              v-model="collectionForm.collectionUrl"
              rows="3"
              placeholder="请输入要采集的商品链接或搜索页面链接，每行一个..."
              class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
            <div class="mt-1 text-xs text-gray-500">支持商品详情页链接、搜索结果页链接、分类页链接等</div>
          </div>

          <!-- 关键词搜索 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">关键词搜索</label>
            <input
              v-model="collectionForm.keywords"
              type="text"
              placeholder="请输入搜索关键词，多个关键词用逗号分隔"
              class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
            <div class="mt-1 text-xs text-gray-500">如：手机壳,保护套,iPhone配件</div>
          </div>

          <!-- 价格范围 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">价格范围</label>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">最低价</span>
                <input
                  v-model.number="collectionForm.priceRange.min"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0"
                  class="w-24 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <span class="text-sm text-gray-600">元</span>
              </div>
              <span class="text-gray-400">-</span>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">最高价</span>
                <input
                  v-model.number="collectionForm.priceRange.max"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="不限"
                  class="w-24 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <span class="text-sm text-gray-600">元</span>
              </div>
            </div>
          </div>

          <!-- 评分筛选 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">最低评分</label>
            <div class="flex items-center space-x-2">
              <input
                v-model.number="collectionForm.ratingFilter"
                type="number"
                min="1"
                max="5"
                step="0.1"
                class="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <span class="text-sm text-gray-600">星</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">只采集评分高于此值的商品</div>
          </div>

          <!-- 销量筛选 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">最低销量</label>
            <div class="flex items-center space-x-2">
              <input
                v-model.number="collectionForm.salesFilter"
                type="number"
                min="0"
                class="w-32 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <span class="text-sm text-gray-600">件</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">只采集销量高于此值的商品</div>
          </div>

          <!-- 采集数量限制 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">采集数量限制</label>
            <div class="flex items-center space-x-2">
              <input
                v-model.number="collectionForm.maxItems"
                type="number"
                min="1"
                max="1000"
                class="w-32 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <span class="text-sm text-gray-600">个商品</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">单次采集的最大商品数量</div>
          </div>

          <!-- 自动采集 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">自动采集</label>
            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <label class="flex items-center cursor-pointer">
                  <input
                    v-model="collectionForm.autoCollection"
                    type="checkbox"
                    class="mr-2"
                  >
                  <span class="text-sm">{{ collectionForm.autoCollection ? '启用' : '关闭' }}</span>
                </label>
              </div>
              <div v-if="collectionForm.autoCollection" class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">采集间隔</span>
                <input
                  v-model.number="collectionForm.collectionInterval"
                  type="number"
                  min="30"
                  class="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <span class="text-sm text-gray-600">分钟</span>
              </div>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>启用后，系统将按设定间隔自动执行采集任务。</li>
                <li>建议间隔时间不少于30分钟，避免频繁请求被限制。</li>
              </ul>
            </div>
          </div>

          <!-- 采集内容选项 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">采集内容</label>
            <div class="space-y-2">
              <label class="flex items-center cursor-pointer">
                <input
                  v-model="collectionForm.collectImages"
                  type="checkbox"
                  class="mr-2"
                >
                <span class="text-sm">采集商品图片</span>
              </label>
              <label class="flex items-center cursor-pointer">
                <input
                  v-model="collectionForm.collectReviews"
                  type="checkbox"
                  class="mr-2"
                >
                <span class="text-sm">采集商品评价</span>
              </label>
              <div v-if="collectionForm.collectReviews" class="ml-6 flex items-center space-x-2">
                <span class="text-sm text-gray-600">最多采集</span>
                <input
                  v-model.number="collectionForm.maxReviews"
                  type="number"
                  min="1"
                  max="200"
                  class="w-20 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <span class="text-sm text-gray-600">条评价</span>
              </div>
            </div>
          </div>

          <!-- 代理设置 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">代理设置</label>
            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <label class="flex items-center cursor-pointer">
                  <input
                    v-model="collectionForm.enableProxy"
                    type="checkbox"
                    class="mr-2"
                  >
                  <span class="text-sm">{{ collectionForm.enableProxy ? '启用代理' : '不使用代理' }}</span>
                </label>
              </div>
              <div v-if="collectionForm.enableProxy">
                <textarea
                  v-model="collectionForm.proxyConfig"
                  rows="3"
                  placeholder="请输入代理配置，格式：http://username:<EMAIL>:8080"
                  class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>使用代理可以提高采集成功率，避免IP被封禁。</li>
                <li>支持HTTP/HTTPS代理，格式：协议://用户名:密码@代理地址:端口</li>
              </ul>
            </div>
          </div>

          <!-- 过滤规则 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">过滤规则</label>
            <textarea
              v-model="collectionForm.filterRules"
              rows="4"
              placeholder="请输入过滤规则，每行一个规则..."
              class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            ></textarea>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>支持标题关键词过滤：title:包含关键词</li>
                <li>支持价格范围过滤：price:10-100</li>
                <li>支持品牌过滤：brand:Apple,Samsung</li>
                <li>支持排除关键词：exclude:二手,翻新</li>
              </ul>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-center space-x-4 pt-6">
            <button
              type="button"
              class="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              @click="() => { /* 预览采集结果 */ }"
            >
              预览采集
            </button>
            <button
              type="submit"
              class="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              开始采集
            </button>
          </div>
        </form>
        </div>
      </div>
    </div>
  </div>
</template>
