// 增强版触发脚本 - 专门用来触发包含anti-content的真实业务请求
// 在Temu商家后台页面控制台运行

console.log('🚀 增强版anti-content触发脚本');

window.enhancedTrigger = {
    // 1. 检查当前缓存状态
    checkCache: function() {
        const sources = [
            'ultimate_anti_content',
            'temu_cached_anti_content', 
            'optimized_anti_content'
        ];
        
        for (const source of sources) {
            const cached = localStorage.getItem(source);
            if (cached) {
                console.log(`✅ 找到缓存 ${source}:`, cached.substring(0, 50) + '...');
                return cached;
            }
        }
        
        console.log('❌ 没有找到anti-content缓存');
        return null;
    },

    // 2. 导航到不同的页面（这些页面更可能触发包含anti-content的请求）
    navigateToBusinessPages: async function() {
        console.log('🧭 导航到业务页面...');
        
        const businessPages = [
            '/main/product/seller-select',  // 商品选品页面
            '/main/product/list',           // 商品列表
            '/main/order/list',             // 订单列表  
            '/main/data/overview',          // 数据概览
            '/main/finance/settlement',     // 财务结算
            '/main/promotion/list'          // 推广活动
        ];
        
        for (const page of businessPages) {
            try {
                console.log(`🧭 导航到: ${page}`);
                
                // 方法1: 直接修改URL
                if (window.location.pathname !== page) {
                    window.history.pushState({}, '', page);
                    
                    // 触发路由变化事件
                    window.dispatchEvent(new PopStateEvent('popstate'));
                    
                    // 等待页面加载
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    
                    // 检查是否获取到数据
                    const cached = this.checkCache();
                    if (cached) {
                        console.log('🎉 在页面导航后获取到anti-content！');
                        return true;
                    }
                }
            } catch (error) {
                console.warn(`导航到 ${page} 失败:`, error);
            }
        }
        
        return false;
    },

    // 3. 点击页面中的各种业务按钮
    clickBusinessButtons: function() {
        console.log('🖱️ 点击业务按钮...');
        
        const businessSelectors = [
            // 数据刷新按钮
            'button[title*="刷新"]',
            'button[title*="refresh"]',
            'button[class*="refresh"]',
            
            // 查询按钮
            'button[title*="查询"]',
            'button[title*="搜索"]',
            'button[class*="search"]',
            'button[class*="query"]',
            
            // 列表操作按钮
            '.ant-btn-primary',
            '.ant-btn-default',
            
            // 菜单项
            '.ant-menu-item',
            '.menu-item',
            
            // 标签页
            '.ant-tabs-tab',
            
            // 分页按钮
            '.ant-pagination-item',
            
            // 下拉菜单
            '.ant-select',
            '.ant-dropdown-trigger'
        ];
        
        let totalClicked = 0;
        
        businessSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                console.log(`🔍 找到 ${selector}:`, elements.length);
                
                elements.forEach((element, index) => {
                    if (index < 2 && totalClicked < 10) { // 限制点击数量
                        try {
                            // 检查元素是否可见和可点击
                            const rect = element.getBoundingClientRect();
                            if (rect.width > 0 && rect.height > 0) {
                                console.log(`🖱️ 点击 ${selector}[${index}]:`, element);
                                element.click();
                                totalClicked++;
                                
                                // 每次点击后等待一下
                                setTimeout(() => {
                                    this.checkCache();
                                }, 1000);
                            }
                        } catch (error) {
                            console.warn('点击失败:', error);
                        }
                    }
                });
            } catch (error) {
                console.warn(`查找 ${selector} 失败:`, error);
            }
        });
        
        console.log(`✅ 总共点击了 ${totalClicked} 个元素`);
        return totalClicked;
    },

    // 4. 模拟用户在页面中的真实操作
    simulateRealUserActions: async function() {
        console.log('👤 模拟真实用户操作...');
        
        // 操作序列
        const actions = [
            // 滚动页面
            () => {
                console.log('📜 滚动页面...');
                window.scrollTo(0, 300);
                setTimeout(() => window.scrollTo(0, 0), 1000);
            },
            
            // 点击左侧菜单
            () => {
                console.log('📋 点击左侧菜单...');
                const menuItems = document.querySelectorAll('.ant-menu-item, .menu-item, [role="menuitem"]');
                if (menuItems.length > 0) {
                    const randomItem = menuItems[Math.floor(Math.random() * Math.min(menuItems.length, 3))];
                    randomItem.click();
                }
            },
            
            // 点击标签页
            () => {
                console.log('📑 点击标签页...');
                const tabs = document.querySelectorAll('.ant-tabs-tab');
                if (tabs.length > 0) {
                    const randomTab = tabs[Math.floor(Math.random() * tabs.length)];
                    randomTab.click();
                }
            },
            
            // 触发下拉菜单
            () => {
                console.log('📋 触发下拉菜单...');
                const dropdowns = document.querySelectorAll('.ant-select, .ant-dropdown-trigger');
                if (dropdowns.length > 0) {
                    const randomDropdown = dropdowns[Math.floor(Math.random() * dropdowns.length)];
                    randomDropdown.click();
                }
            }
        ];
        
        // 执行操作序列
        for (let i = 0; i < actions.length; i++) {
            try {
                actions[i]();
                
                // 每个操作后等待并检查
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const cached = this.checkCache();
                if (cached) {
                    console.log(`🎉 在第${i+1}个操作后获取到anti-content！`);
                    return true;
                }
            } catch (error) {
                console.warn(`操作${i+1}失败:`, error);
            }
        }
        
        return false;
    },

    // 5. 强制刷新页面数据
    forceRefreshData: async function() {
        console.log('🔄 强制刷新页面数据...');
        
        // 尝试按F5刷新
        try {
            const event = new KeyboardEvent('keydown', {
                key: 'F5',
                code: 'F5',
                keyCode: 116,
                which: 116
            });
            document.dispatchEvent(event);
            
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const cached = this.checkCache();
            if (cached) {
                console.log('🎉 页面刷新后获取到anti-content！');
                return true;
            }
        } catch (error) {
            console.warn('F5刷新失败:', error);
        }
        
        // 尝试Ctrl+R刷新
        try {
            const event = new KeyboardEvent('keydown', {
                key: 'r',
                code: 'KeyR',
                ctrlKey: true
            });
            document.dispatchEvent(event);
            
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const cached = this.checkCache();
            if (cached) {
                console.log('🎉 Ctrl+R刷新后获取到anti-content！');
                return true;
            }
        } catch (error) {
            console.warn('Ctrl+R刷新失败:', error);
        }
        
        return false;
    },

    // 6. 运行完整的增强触发流程
    runEnhancedTrigger: async function() {
        console.log('🚀 开始增强触发流程...');
        console.log('='.repeat(60));
        
        // 1. 检查初始状态
        console.log('1️⃣ 检查初始状态');
        let cached = this.checkCache();
        if (cached) {
            console.log('✅ 已有缓存，无需触发');
            return true;
        }
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 2. 点击业务按钮
        console.log('2️⃣ 点击业务按钮');
        this.clickBusinessButtons();
        
        await new Promise(resolve => setTimeout(resolve, 3000));
        cached = this.checkCache();
        if (cached) {
            console.log('🎉 点击按钮后获取到anti-content！');
            return true;
        }
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 3. 模拟真实用户操作
        console.log('3️⃣ 模拟真实用户操作');
        const userActionResult = await this.simulateRealUserActions();
        if (userActionResult) {
            return true;
        }
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 4. 导航到业务页面
        console.log('4️⃣ 导航到业务页面');
        const navigationResult = await this.navigateToBusinessPages();
        if (navigationResult) {
            return true;
        }
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 5. 强制刷新数据
        console.log('5️⃣ 强制刷新数据');
        const refreshResult = await this.forceRefreshData();
        if (refreshResult) {
            return true;
        }
        
        console.log('\n' + '='.repeat(60));
        console.log('❌ 增强触发流程完成，但未获取到anti-content');
        console.log('💡 建议：');
        console.log('  1. 手动在页面中进行一些业务操作');
        console.log('  2. 等待页面自动刷新数据');
        console.log('  3. 尝试访问不同的功能模块');
        
        return false;
    }
};

console.log('🔧 增强版触发工具已准备就绪！');
console.log('💡 运行 enhancedTrigger.runEnhancedTrigger() 开始增强触发');
console.log('💡 运行 enhancedTrigger.checkCache() 检查缓存');
console.log('💡 运行 enhancedTrigger.clickBusinessButtons() 点击业务按钮');

// 提示用户最有效的方法
console.log('\n🎯 最有效的方法：');
console.log('1. 运行 enhancedTrigger.runEnhancedTrigger() 自动触发');
console.log('2. 手动点击左侧菜单的"商品管理"、"订单管理"等');
console.log('3. 在商品列表页面进行搜索或筛选操作');
console.log('4. 查看数据统计页面');

// 自动开始
enhancedTrigger.runEnhancedTrigger();
