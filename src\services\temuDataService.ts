// Temu 数据获取服务
// 负责获取商品列表、待办事项等数据

// 待办事项数据接口
interface TodoCountData {
  success: boolean
  errorCode: number
  errorMsg: string | null
  result: {
    total: number
    todoStatusAggregationList: any[] | null
  }
}

// 商品列表数据接口
interface ProductListData {
  success: boolean
  errorCode: number
  errorMsg: string | null
  result: {
    total: number
    productSkcStatusAggregation: Array<{
      selectStatus: number
      count: number
    }>
    productIdNoStatusTotal: number
    recycleCount: number | null
    dataList: ProductItem[]
  }
}

// 商品项目接口
interface ProductItem {
  productId: number
  productName: string
  supplierPrice: string
  supplierPriceCurrencyType: string
  leafCategoryName: string
  fullCategoryName: string[]
  carouselImageUrlList: string[]
  productCreatedAt: number
  productUpdatedAt: number
  siteInfoList: Array<{
    siteId: number
    siteName: string
  }>
  skcList: Array<{
    skcId: number
    selectStatus: number
    supplierPrice: string
    extCode: string
    previewImgUrlList: string[]
    statusTime: {
      createdTime: number
      selectedTime: number | null
    }
    skuList: Array<{
      skuId: number
      skuPreviewImage: string
      selectStatus: string
      productPropertyList: Array<{
        name: string
        value: string
      }>
      extCode: string
      siteSupplierPriceList: Array<{
        siteId: number
        siteName: string
        supplierPrice: string
        supplierPriceValue: number
      }>
    }>
  }>
}

// 搜索参数接口
interface SearchParams {
  pageSize: number
  pageNum: number
  supplierTodoTypeList: number[]
  siteIds?: number[]
  productIds?: string[]
  skuCodes?: string[]
}

class TemuDataService {
  private mallId: string = '';
  private interceptorRegistered: boolean = false;

  constructor() {
    this.init()
  }

  // 初始化服务
  private async init() {
    try {
      console.info('[TemuDataService] 初始化服务...')

      // 注册拦截器
      await this.registerInterceptor()

      console.info('[TemuDataService] 服务初始化完成')
    } catch (error) {
      console.error('[TemuDataService] 服务初始化失败:', error)
    }
  }

  // 注册拦截器来缓存Anti-Content和mallid
  private async registerInterceptor() {
    if (this.interceptorRegistered) {
      console.info('[TemuDataService] 拦截器已注册，跳过')
      return
    }

    try {
      console.info('[TemuDataService] 注册请求拦截器...')

      // 检查是否在扩展环境中
      if (typeof chrome === 'undefined' || !chrome.tabs) {
        console.warn('[TemuDataService] 不在扩展环境中，无法注册拦截器')
        return
      }

      // 向所有Temu标签页注册拦截器
      await this.registerInterceptorInTabs()

      this.interceptorRegistered = true
      console.info('[TemuDataService] 拦截器注册完成')
    } catch (error) {
      console.warn('[TemuDataService] 注册拦截器失败:', error)
    }
  }

  // 在Temu标签页中注册拦截器
  private async registerInterceptorInTabs() {
    try {
      // 查找所有Temu标签页
      const tabs = await chrome.tabs.query({
        url: ['*://seller.kuajingmaihuo.com/*', '*://seller.temu.com/*']
      })

      console.info('[TemuDataService] 找到Temu标签页数量:', tabs.length)

      // 向每个标签页发送注册拦截器的消息
      for (const tab of tabs) {
        if (tab.id) {
          try {
            await chrome.tabs.sendMessage(tab.id, {
              action: 'REGISTER_INTERCEPTOR'
            })
            console.info('[TemuDataService] 向标签页注册拦截器成功:', tab.id)
          } catch (error) {
            console.warn('[TemuDataService] 向标签页注册拦截器失败:', tab.id, error)
          }
        }
      }
    } catch (error) {
      console.warn('[TemuDataService] 查找Temu标签页失败:', error)
    }
  }

  // 设置店铺ID
  setMallId(mallId: string): void {
    this.mallId = mallId
    console.info('[TemuDataService] 设置店铺ID:', mallId)
  }

  // 获取请求头
  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'accept': '*/*',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'application/json',
      'cache-control': 'max-age=0',
      'origin': 'https://seller.kuajingmaihuo.com',
      'referer': 'https://seller.kuajingmaihuo.com/',
      'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
      'priority': 'u=1, i'
    }

    // 添加店铺ID到请求头
    if (this.mallId) {
      headers['mallid'] = this.mallId
    }

    return headers
  }

  // 获取 anti-content 头部（优先从localStorage获取）
  private async getAntiContentHeader(): Promise<string | null> {
    try {
      // 1. 优先从localStorage获取（基于成功的拦截结果）
      const cachedAntiContent = this.getAntiContentFromStorage()
      if (cachedAntiContent) {
        console.info('[TemuDataService] 使用localStorage缓存的 anti-content')
        return cachedAntiContent
      }

      // 2. 通过 content script 获取
      if (typeof chrome !== 'undefined' && chrome.tabs) {
        const tabs = await chrome.tabs.query({
          url: ['*://seller.kuajingmaihuo.com/*', '*://seller.temu.com/*']
        })

        if (tabs.length > 0) {
          const response = await chrome.tabs.sendMessage(tabs[0].id!, {
            action: 'GET_ANTI_CONTENT'
          })

          if (response && response.antiContent) {
            console.info('[TemuDataService] 从content script获取到 anti-content')
            return response.antiContent
          }
        }
      }
    } catch (error) {
      console.warn('[TemuDataService] 获取 anti-content 失败:', error)
    }

    return null
  }

  // 从localStorage获取anti-content（支持多种缓存来源）
  private getAntiContentFromStorage(): string | null {
    try {
      // 检查多种可能的缓存来源（基于成功的拦截结果）
      const sources = [
        'ultimate_anti_content',      // 终极调试脚本的缓存
        'temu_cached_anti_content',   // 早期拦截器的缓存
        'optimized_anti_content'      // 优化测试的缓存
      ]

      for (const source of sources) {
        const cached = localStorage.getItem(source)
        if (cached) {
          console.info('[TemuDataService] 从localStorage获取anti-content:', source)
          return cached
        }
      }
    } catch (error) {
      console.warn('[TemuDataService] 从localStorage获取anti-content失败:', error)
    }

    return null
  }

  // 从localStorage获取mallId
  private getMallIdFromStorage(): string | null {
    try {
      const sources = [
        'ultimate_mall_id',      // 终极调试脚本的缓存
        'temu_cached_mall_id',   // 早期拦截器的缓存
        'optimized_mall_id'      // 优化测试的缓存
      ]

      for (const source of sources) {
        const cached = localStorage.getItem(source)
        if (cached) {
          console.info('[TemuDataService] 从localStorage获取mallId:', source, cached)
          return cached
        }
      }
    } catch (error) {
      console.warn('[TemuDataService] 从localStorage获取mallId失败:', error)
    }

    return null
  }

  // 获取待办事项数量
  async getTodoCount(): Promise<TodoCountData> {
    console.info('[TemuDataService] 开始获取待办事项数量...')

    // 优先使用 content script 方案，因为它能获取到正确的 Anti-Content 和 mallid
    try {
      console.info('[TemuDataService] 优先尝试通过 content script 获取待办事项数据...')
      return await this.getDataViaContentScript('todo')
    } catch (contentScriptError) {
      console.warn('[TemuDataService] content script 方案失败，尝试直接API调用...', contentScriptError)

      // 备用方案：直接API调用
      try {
        const headers = this.getHeaders()

        // 尝试获取 anti-content（基于成功的拦截结果使用小写）
        const antiContent = await this.getAntiContentHeader()
        if (antiContent) {
          headers['anti-content'] = antiContent
          console.info('[TemuDataService] 已添加 anti-content 到请求头')
        } else {
          console.warn('[TemuDataService] 未能获取 anti-content，可能导致403错误')
        }

        // 尝试获取 mallid
        const mallIdFromStorage = this.getMallIdFromStorage()
        if (mallIdFromStorage && !this.mallId) {
          this.mallId = mallIdFromStorage
          headers['mallid'] = mallIdFromStorage
          console.info('[TemuDataService] 从localStorage获取并设置 mallid:', mallIdFromStorage)
        }

        // 检查 mallId
        if (!this.mallId) {
          console.warn('[TemuDataService] mallId 为空，可能导致403错误')
        }

        console.info('[TemuDataService] 发送API请求，headers:', headers)

        const response = await fetch('https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount', {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify({})
        })

        if (!response.ok) {
          console.error('[TemuDataService] API 响应错误:', response.status, response.statusText)
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        console.info('[TemuDataService] 待办事项数据:', data)
        return data
      } catch (apiError) {
        console.error('[TemuDataService] 直接API调用也失败:', apiError)
        throw apiError
      }
    }
  }

  // 获取商品列表
  async getProductList(params: Partial<SearchParams> = {}): Promise<ProductListData> {
    console.info('[TemuDataService] 开始获取商品列表...', params)

    const defaultParams: SearchParams = {
      pageSize: 50,
      pageNum: 1,
      supplierTodoTypeList: []
    }
    const searchParams = { ...defaultParams, ...params }

    // 优先使用 content script 方案，因为它能获取到正确的 Anti-Content 和 mallid
    try {
      console.info('[TemuDataService] 优先尝试通过 content script 获取商品列表数据...')
      return await this.getDataViaContentScript('products', searchParams)
    } catch (contentScriptError) {
      console.warn('[TemuDataService] content script 方案失败，尝试直接API调用...', contentScriptError)

      // 备用方案：直接API调用
      try {
        const headers = this.getHeaders()

        // 尝试获取 anti-content（基于成功的拦截结果使用小写）
        const antiContent = await this.getAntiContentHeader()
        if (antiContent) {
          headers['anti-content'] = antiContent
          console.info('[TemuDataService] 已添加 anti-content 到请求头')
        } else {
          console.warn('[TemuDataService] 未能获取 anti-content，可能导致403错误')
        }

        // 尝试获取 mallid
        const mallIdFromStorage = this.getMallIdFromStorage()
        if (mallIdFromStorage && !this.mallId) {
          this.mallId = mallIdFromStorage
          headers['mallid'] = mallIdFromStorage
          console.info('[TemuDataService] 从localStorage获取并设置 mallid:', mallIdFromStorage)
        }

        // 检查 mallId
        if (!this.mallId) {
          console.warn('[TemuDataService] mallId 为空，可能导致403错误')
        }

        console.info('[TemuDataService] 发送API请求，headers:', headers)

        const response = await fetch('https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier', {
          method: 'POST',
          headers,
          credentials: 'include',
          body: JSON.stringify(searchParams)
        })

        if (!response.ok) {
          console.error('[TemuDataService] API 响应错误:', response.status, response.statusText)
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        console.info('[TemuDataService] 商品列表数据:', data)
        return data
      } catch (apiError) {
        console.error('[TemuDataService] 直接API调用也失败:', apiError)
        throw apiError
      }
    }
  }

  // 调试 Anti-Content 获取
  async debugAntiContent(): Promise<{ antiContent: string | null; debug: boolean }> {
    try {
      console.info('[TemuDataService] 开始调试 Anti-Content 获取...')

      if (typeof chrome === 'undefined' || !chrome.tabs) {
        throw new Error('Chrome API 不可用')
      }

      // 查找 Temu 标签页
      const tabs = await chrome.tabs.query({
        url: ['*://seller.kuajingmaihuo.com/*', '*://seller.temu.com/*']
      })

      if (tabs.length === 0) {
        throw new Error('未找到 Temu 商家后台标签页')
      }

      // 发送调试消息到 content script
      const response = await chrome.tabs.sendMessage(tabs[0].id!, {
        action: 'DEBUG_ANTI_CONTENT'
      })

      console.info('[TemuDataService] 调试响应:', response)
      return response
    } catch (error) {
      console.error('[TemuDataService] 调试 Anti-Content 失败:', error)
      throw error
    }
  }

  // 通过 content script 获取数据（备用方案）
  async getDataViaContentScript(apiType: 'todo' | 'products', params?: any): Promise<any> {
    try {
      console.info('[TemuDataService] 通过 content script 获取数据...', apiType, params)

      if (typeof chrome === 'undefined' || !chrome.tabs) {
        throw new Error('Chrome API 不可用')
      }

      // 查找 Temu 标签页
      const tabs = await chrome.tabs.query({ 
        url: ['*://seller.kuajingmaihuo.com/*', '*://seller.temu.com/*'] 
      })
      
      if (tabs.length === 0) {
        throw new Error('未找到 Temu 商家后台标签页')
      }

      const response = await chrome.tabs.sendMessage(tabs[0].id!, {
        action: 'GET_TEMU_DATA',
        apiType,
        params,
        mallId: this.mallId
      })

      if (response && response.success) {
        return response.data
      } else {
        throw new Error(response?.error || '获取数据失败')
      }
    } catch (error) {
      console.error('[TemuDataService] 通过 content script 获取数据失败:', error)
      throw error
    }
  }

  // 格式化商品数据为显示格式
  formatProductData(productList: ProductListData): any[] {
    console.info('[TemuDataService] 开始格式化商品数据...', productList)

    if (!productList.result || !productList.result.dataList) {
      console.warn('[TemuDataService] 没有找到dataList数据')
      return []
    }

    console.info('[TemuDataService] 找到商品数据:', productList.result.dataList.length, '个商品')

    return productList.result.dataList.map((product, index) => {
      console.info(`[TemuDataService] 处理第${index + 1}个商品:`, product.productName)

      const firstSkc = product.skcList?.[0]
      const firstSku = firstSkc?.skuList?.[0]

      console.info(`[TemuDataService] SKC数据:`, firstSkc)
      console.info(`[TemuDataService] SKU数据:`, firstSku)

      const formattedProduct = {
        id: product.productId,
        image: product.carouselImageUrlList?.[0] || '',
        title: product.productName || '',
        spu: product.productId?.toString() || '',
        skc: firstSkc?.skcId?.toString() || '',
        site: product.siteInfoList?.[0]?.siteName || '',
        currency: product.supplierPriceCurrencyType || '',
        category: product.fullCategoryName?.join('>') || '',
        declaredPrice: product.supplierPrice || '',
        createTime: product.productCreatedAt ? new Date(product.productCreatedAt).toLocaleString('zh-CN') : '',
        sku: {
          image: firstSku?.skuPreviewImage || firstSkc?.previewImgUrlList?.[0] || '',
          color: firstSku?.productPropertyList?.find(p => p.name === '颜色')?.value || '',
          itemNo: firstSku?.extCode || firstSkc?.extCode || '',
          status: this.getStatusText(firstSkc?.selectStatus),
          price: firstSkc?.supplierPrice || firstSku?.siteSupplierPriceList?.[0]?.supplierPrice || product.supplierPrice || ''
        }
      }

      console.info(`[TemuDataService] 格式化后的商品${index + 1}:`, formattedProduct)
      return formattedProduct
    })
  }

  // 获取状态文本
  private getStatusText(status?: number): string {
    const statusMap: Record<number, string> = {
      1: '已选中',
      7: '价格申报中',
      9: '价格待确认',
      10: '未发布到站点',
      11: '待发布到站点',
      12: '已发布站点',
      13: '已下架'
    }

    const statusText = statusMap[status || 0] || `状态${status || 0}`
    console.info(`[TemuDataService] 状态映射: ${status} -> ${statusText}`)
    return statusText
  }

  // 获取状态统计数据
  formatStatusTabs(productList: ProductListData): any[] {
    console.info('[TemuDataService] 开始格式化状态标签页...', productList.result)

    if (!productList.result) {
      console.warn('[TemuDataService] 没有找到result数据')
      return [{ key: 'all', label: '全部', count: 0 }]
    }

    const statusMap: Record<number, { key: string; label: string }> = {
      1: { key: 'selected', label: '已选中' },
      7: { key: 'price-reporting', label: '价格申报中' },
      9: { key: 'price-confirming', label: '价格待确认' },
      10: { key: 'unpublished', label: '未发布到站点' },
      12: { key: 'published', label: '已发布站点' },
      13: { key: 'offline', label: '已下架' }
    }

    const tabs = [
      { key: 'all', label: '全部', count: productList.result.total || 0 }
    ]

    if (productList.result.productSkcStatusAggregation) {
      console.info('[TemuDataService] 状态统计数据:', productList.result.productSkcStatusAggregation)

      productList.result.productSkcStatusAggregation.forEach(item => {
        const statusInfo = statusMap[item.selectStatus]
        if (statusInfo) {
          tabs.push({
            key: statusInfo.key,
            label: statusInfo.label,
            count: item.count
          })
        } else {
          console.warn('[TemuDataService] 未知状态:', item.selectStatus)
          tabs.push({
            key: `status-${item.selectStatus}`,
            label: `状态${item.selectStatus}`,
            count: item.count
          })
        }
      })
    } else {
      console.warn('[TemuDataService] 没有找到productSkcStatusAggregation数据')
    }

    console.info('[TemuDataService] 格式化后的标签页:', tabs)
    return tabs
  }

  // 创建单例实例
}

export default new TemuDataService();
export type { TodoCountData, ProductListData, ProductItem, SearchParams }
