// 早期拦截器 - 在 document_start 阶段运行，确保能拦截到所有网络请求
console.info('[Early Interceptor] 🚀 启动早期拦截器...')

// 全局状态
const interceptorState = {
    interceptedRequests: [] as any[],
    antiContentFound: null as string | null,
    mallIdFound: null as string | null
}

// 全局缓存对象
const globalCache = {
    antiContent: null as string | null,
    antiContentExpiry: 0,
    mallId: null as string | null,
    mallIdExpiry: 0
}

// 缓存 anti-content 函数
function cacheAntiContent(value: string) {
    try {
        const expiry = Date.now() + 30 * 60 * 1000; // 30分钟
        localStorage.setItem('temu_cached_anti_content', value);
        localStorage.setItem('temu_anti_content_expiry', expiry.toString());
        localStorage.setItem('temu_cs_anti_content', value);
        localStorage.setItem('temu_cs_anti_content_expiry', expiry.toString());
        localStorage.setItem('ultimate_anti_content', value);
        localStorage.setItem('ultimate_anti_content_time', Date.now().toString());
        
        globalCache.antiContent = value;
        globalCache.antiContentExpiry = expiry;
        interceptorState.antiContentFound = value;
        
        console.info('[Early Interceptor] ✅ 成功缓存 anti-content');
    } catch (e) {
        console.warn('[Early Interceptor] 缓存 anti-content 失败:', e);
    }
}

// 缓存 mallId 函数
function cacheMallId(value: string) {
    try {
        const expiry = Date.now() + 60 * 60 * 1000; // 60分钟
        localStorage.setItem('temu_cached_mall_id', value);
        localStorage.setItem('temu_mall_id_expiry', expiry.toString());
        localStorage.setItem('temu_cs_mall_id', value);
        localStorage.setItem('temu_cs_mall_id_expiry', expiry.toString());
        localStorage.setItem('ultimate_mall_id', value);
        localStorage.setItem('ultimate_mall_id_time', Date.now().toString());
        
        globalCache.mallId = value;
        globalCache.mallIdExpiry = expiry;
        interceptorState.mallIdFound = value;
        
        console.info('[Early Interceptor] ✅ 成功缓存 mallId');
    } catch (e) {
        console.warn('[Early Interceptor] 缓存 mallId 失败:', e);
    }
}

// 检查请求头的通用函数（完全复制ultimate-debug.js的逻辑）
function checkHeaders(headers: Record<string, string>, source: string, url: string) {
    console.info(`[Early Interceptor] 📋 检查${source}请求头:`, headers)

    // 检查anti-content的所有可能变体
    const antiContentKeys = [
        'anti-content', 'Anti-Content', 'ANTI-CONTENT', 'antiContent',
        'anti_content', 'Anti_Content', 'ANTI_CONTENT'
    ]

    let foundAntiContent = null
    let foundKey = null

    for (const key of antiContentKeys) {
        if (headers[key]) {
            foundAntiContent = headers[key]
            foundKey = key
            break
        }
    }

    if (foundAntiContent) {
        ultimateDebugState.antiContentFound = foundAntiContent
        console.info(`[Early Interceptor] 🎉 找到 anti-content (${source}, key: ${foundKey}):`, foundAntiContent)

        // 保存到localStorage（完全复制ultimate-debug.js的逻辑）
        try {
            localStorage.setItem('ultimate_anti_content', foundAntiContent)
            localStorage.setItem('ultimate_anti_content_source', source)
            localStorage.setItem('ultimate_anti_content_key', foundKey)
            localStorage.setItem('ultimate_anti_content_time', Date.now().toString())
            localStorage.setItem('ultimate_anti_content_url', url)

            // 兼容扩展的键名
            localStorage.setItem('temu_cached_anti_content', foundAntiContent)
            localStorage.setItem('temu_anti_content_expiry', (Date.now() + 30 * 60 * 1000).toString())

            console.info('[Early Interceptor] ✅ 成功保存 anti-content 到localStorage')
        } catch (e) {
            console.warn('[Early Interceptor] 保存anti-content失败:', e)
        }
    }

    // 检查mallid的所有可能变体
    const mallIdKeys = ['mallid', 'mallId', 'MallId', 'MALLID', 'mall_id', 'Mall_Id']

    let foundMallId = null
    let foundMallKey = null

    for (const key of mallIdKeys) {
        if (headers[key]) {
            foundMallId = headers[key]
            foundMallKey = key
            break
        }
    }

    if (foundMallId) {
        ultimateDebugState.mallIdFound = foundMallId
        console.info(`[Early Interceptor] 🎉 找到 mallId (${source}, key: ${foundMallKey}):`, foundMallId)

        // 保存到localStorage
        try {
            localStorage.setItem('ultimate_mall_id', foundMallId)
            localStorage.setItem('ultimate_mall_id_source', source)
            localStorage.setItem('ultimate_mall_id_key', foundMallKey)
            localStorage.setItem('ultimate_mall_id_time', Date.now().toString())

            // 兼容扩展的键名
            localStorage.setItem('temu_cached_mall_id', foundMallId)
            localStorage.setItem('temu_mall_id_expiry', (Date.now() + 60 * 60 * 1000).toString())

            console.info('[Early Interceptor] ✅ 成功保存 mallId 到localStorage')
        } catch (e) {
            console.warn('[Early Interceptor] 保存mallId失败:', e)
        }
    }

    // 记录请求（完全复制ultimate-debug.js）
    ultimateDebugState.interceptedRequests.push({
        source,
        url,
        headers,
        antiContent: foundAntiContent,
        mallId: foundMallId,
        timestamp: Date.now()
    })
}

// 从localStorage加载缓存
function loadCache() {
  try {
    // 加载anti-content
    const cachedAntiContent = localStorage.getItem('temu_cached_anti_content')
    const antiContentExpiry = localStorage.getItem('temu_anti_content_expiry')
    
    if (cachedAntiContent && antiContentExpiry) {
      const expiry = parseInt(antiContentExpiry)
      if (Date.now() < expiry) {
        globalCache.antiContent = cachedAntiContent
        globalCache.antiContentExpiry = expiry
        console.info('[Early Interceptor] 从缓存加载 anti-content:', cachedAntiContent.substring(0, 50) + '...')
      } else {
        localStorage.removeItem('temu_cached_anti_content')
        localStorage.removeItem('temu_anti_content_expiry')
      }
    }

    // 加载mallId
    const cachedMallId = localStorage.getItem('temu_cached_mall_id')
    const mallIdExpiry = localStorage.getItem('temu_mall_id_expiry')
    
    if (cachedMallId && mallIdExpiry) {
      const expiry = parseInt(mallIdExpiry)
      if (Date.now() < expiry) {
        globalCache.mallId = cachedMallId
        globalCache.mallIdExpiry = expiry
        console.info('[Early Interceptor] 从缓存加载 mallId:', cachedMallId)
      } else {
        localStorage.removeItem('temu_cached_mall_id')
        localStorage.removeItem('temu_mall_id_expiry')
      }
    }
  } catch (error) {
    console.warn('[Early Interceptor] 加载缓存失败:', error)
  }
}

// 设置fetch拦截（完全复制ultimate-debug.js的成功逻辑）
function setupFetchInterception() {
  const originalFetch = window.fetch

  window.fetch = function(...args) {
    const [url, options] = args
    const urlString = typeof url === 'string' ? url : url.toString()

    if (urlString.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🎯 拦截fetch请求:', urlString)
      console.info('[Early Interceptor] 📋 请求选项:', options)

      if (options && options.headers) {
        checkHeaders(options.headers as Record<string, string>, 'fetch', urlString)
      }
    }

    return originalFetch.apply(this, args)
  }

  console.info('[Early Interceptor] ✅ Fetch拦截器已设置')
}

// 检查请求头的通用函数（完全复制ultimate-debug.js的逻辑）
function checkHeaders(headers: Record<string, string>, source: string, url: string) {
  console.info(`[Early Interceptor] 📋 检查${source}请求头:`, headers)

  // 检查anti-content的所有可能变体
  const antiContentKeys = [
    'anti-content', 'Anti-Content', 'ANTI-CONTENT', 'antiContent',
    'anti_content', 'Anti_Content', 'ANTI_CONTENT'
  ]

  let foundAntiContent = null
  let foundKey = null

  for (const key of antiContentKeys) {
    if (headers[key]) {
      foundAntiContent = headers[key]
      foundKey = key
      break
    }
  }

  if (foundAntiContent) {
    console.info(`[Early Interceptor] 🎉 找到 anti-content (${source}, key: ${foundKey}):`, foundAntiContent)
    cacheAntiContent(foundAntiContent)
  }

  // 检查mallid的所有可能变体
  const mallIdKeys = ['mallid', 'mallId', 'MallId', 'MALLID', 'mall_id', 'Mall_Id']

  let foundMallId = null
  let foundMallKey = null

  for (const key of mallIdKeys) {
    if (headers[key]) {
      foundMallId = headers[key]
      foundMallKey = key
      break
    }
  }

  if (foundMallId) {
    console.info(`[Early Interceptor] 🎉 找到 mallId (${source}, key: ${foundMallKey}):`, foundMallId)
    cacheMallId(foundMallId)
  }
}

// 检查单个请求头（完全复制ultimate-debug.js的逻辑）
function checkSingleHeader(name: string, value: string, source: string, url: string) {
  const lowerName = name.toLowerCase()

  if (lowerName.includes('anti') && lowerName.includes('content')) {
    console.info(`[Early Interceptor] 🎉 找到 anti-content (${source}, header: ${name}):`, value)
    cacheAntiContent(value)
  }

  if (lowerName.includes('mall') && lowerName.includes('id')) {
    console.info(`[Early Interceptor] 🎉 找到 mallId (${source}, header: ${name}):`, value)
    cacheMallId(value)
  }
}

// 设置XMLHttpRequest拦截
function setupXHRInterception() {
  const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader
  const originalOpen = XMLHttpRequest.prototype.open
  const originalSend = XMLHttpRequest.prototype.send

  // 存储XHR实例信息
  const xhrInstances = new WeakMap<XMLHttpRequest, { url: string; headers: Record<string, string> }>()

  XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
    const urlString = url.toString()
    xhrInstances.set(this, { url: urlString, headers: {} })

    if (urlString.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🌐 XHR open:', method, urlString)
    }

    return originalOpen.apply(this, [method, url, ...args])
  }

  XMLHttpRequest.prototype.setRequestHeader = function(name: string, value: string) {
    const instance = xhrInstances.get(this)
    if (instance) {
      instance.headers[name] = value

      if (instance.url.includes('seller.kuajingmaihuo.com')) {
        console.info('[Early Interceptor] 🎯 XHR设置头部:', name, '=', value.substring(0, 100) + (value.length > 100 ? '...' : ''))
        checkSingleHeader(name, value, 'XHR', instance.url)
      }
    }

    return originalSetRequestHeader.call(this, name, value)
  }

  XMLHttpRequest.prototype.send = function(body?: any) {
    const instance = xhrInstances.get(this)
    if (instance && instance.url.includes('seller.kuajingmaihuo.com')) {
      console.info('[Early Interceptor] 🎯 XHR发送请求:', instance.url)
      console.info('[Early Interceptor] 📋 所有头部:', instance.headers)
      checkHeaders(instance.headers, 'XHR', instance.url)
    }

    return originalSend.call(this, body)
  }

  console.info('[Early Interceptor] ✅ XHR拦截器已设置')
}

// 暴露全局访问接口（复制ultimate-debug.js的接口）
function exposeGlobalInterface() {
  ;(window as any).temuEarlyInterceptor = {
    getCache: () => ultimateDebugState,
    getCachedAntiContent: () => {
      return ultimateDebugState.antiContentFound
    },
    getCachedMallId: () => {
      return ultimateDebugState.mallIdFound
    },
    getState: () => ultimateDebugState,
    report: () => {
      console.log('📊 Early Interceptor 报告:', {
        timestamp: new Date().toLocaleString(),
        interceptedRequests: ultimateDebugState.interceptedRequests.length,
        antiContentFound: ultimateDebugState.antiContentFound,
        mallIdFound: ultimateDebugState.mallIdFound
      })
      return ultimateDebugState
    },
    clearCache: () => {
      ultimateDebugState.antiContentFound = null
      ultimateDebugState.mallIdFound = null
      ultimateDebugState.interceptedRequests = []
      localStorage.removeItem('ultimate_anti_content')
      localStorage.removeItem('ultimate_mall_id')
      localStorage.removeItem('temu_cached_anti_content')
      localStorage.removeItem('temu_cached_mall_id')
      console.info('[Early Interceptor] 🗑️ 缓存已清除')
    }
  }

  console.info('[Early Interceptor] ✅ 全局接口已暴露: window.temuEarlyInterceptor')
}

// 初始化
function init() {
  console.info('[Early Interceptor] 🚀 开始初始化...')
  
  // 加载现有缓存
  loadCache()
  
  // 设置拦截器
  setupFetchInterception()
  setupXHRInterception()
  
  // 暴露全局接口
  exposeGlobalInterface()
  
  console.info('[Early Interceptor] ✅ 初始化完成')
  console.info('[Early Interceptor] 💾 当前缓存状态:', {
    antiContent: globalCache.antiContent ? globalCache.antiContent.substring(0, 50) + '...' : null,
    mallId: globalCache.mallId
  })
}

// 立即执行初始化
init()

export { globalCache, cacheAntiContent, cacheMallId }
