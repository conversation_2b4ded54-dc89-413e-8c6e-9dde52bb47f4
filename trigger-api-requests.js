// 主动触发包含anti-content的API请求
// 在Temu商家后台页面控制台运行

console.log('🚀 开始主动触发API请求...');

const apiTrigger = {
    // 已知的包含anti-content的API端点
    apiEndpoints: [
        {
            name: '待办事项查询',
            url: 'https://seller.kuajingmaihuo.com/marvel-supplier/api/xmen/select/queryTodoCount',
            method: 'POST',
            body: '{}'
        },
        {
            name: '商品列表查询',
            url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier',
            method: 'POST',
            body: JSON.stringify({
                pageSize: 10,
                pageNum: 1,
                supplierTodoTypeList: []
            })
        },
        {
            name: '红点通知查询',
            url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/venom/purchase/order/queryRedNotice',
            method: 'POST',
            body: '{}'
        }
    ],

    // 1. 模拟点击页面元素触发请求
    simulateUserActions: function() {
        console.log('🖱️ 模拟用户操作...');
        
        // 查找可能触发API请求的元素
        const selectors = [
            'button[class*="refresh"]',
            'button[class*="query"]',
            'button[class*="search"]',
            '.ant-btn',
            '[role="button"]',
            'a[href*="product"]',
            'a[href*="order"]',
            '.menu-item',
            '.nav-item'
        ];
        
        let clickCount = 0;
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            console.log(`🔍 找到 ${selector} 元素:`, elements.length);
            
            elements.forEach((element, index) => {
                if (index < 2 && clickCount < 5) { // 限制点击次数
                    try {
                        console.log(`🖱️ 点击元素:`, element);
                        element.click();
                        clickCount++;
                    } catch (error) {
                        console.warn('点击失败:', error);
                    }
                }
            });
        });
        
        console.log(`✅ 完成模拟点击，共点击 ${clickCount} 个元素`);
    },

    // 2. 尝试直接调用API（可能会失败，但能触发拦截器）
    triggerApiDirectly: async function() {
        console.log('📡 尝试直接调用API...');
        
        // 获取基本的请求头
        const baseHeaders = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'content-type': 'application/json',
            'origin': 'https://seller.kuajingmaihuo.com',
            'referer': window.location.href,
            'user-agent': navigator.userAgent
        };

        for (const api of this.apiEndpoints) {
            try {
                console.log(`🚀 调用 ${api.name}...`);
                
                const response = await fetch(api.url, {
                    method: api.method,
                    headers: baseHeaders,
                    credentials: 'include',
                    body: api.body
                });
                
                console.log(`📡 ${api.name} 响应状态:`, response.status);
                
                if (response.status === 403) {
                    console.log(`⚠️ ${api.name} 返回403，这是预期的（缺少anti-content）`);
                } else if (response.ok) {
                    const data = await response.json();
                    console.log(`✅ ${api.name} 成功:`, data);
                }
                
                // 等待一下再调用下一个
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.log(`❌ ${api.name} 失败:`, error.message);
            }
        }
    },

    // 3. 监听页面变化，等待真实请求
    waitForRealRequests: function() {
        console.log('👀 监听页面变化，等待真实请求...');
        
        // 监听URL变化
        let lastUrl = window.location.href;
        const urlChecker = setInterval(() => {
            if (window.location.href !== lastUrl) {
                lastUrl = window.location.href;
                console.log('🔄 URL变化:', lastUrl);
                
                // URL变化时可能会触发新的API请求
                setTimeout(() => {
                    this.checkForCachedData();
                }, 2000);
            }
        }, 1000);

        // 监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查是否有新的内容加载
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const newButtons = node.querySelectorAll ? node.querySelectorAll('button, a') : [];
                            if (newButtons.length > 0) {
                                console.log('🆕 检测到新的可点击元素:', newButtons.length);
                                // 尝试点击新元素
                                setTimeout(() => {
                                    this.simulateUserActions();
                                }, 1000);
                            }
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 10分钟后停止监听
        setTimeout(() => {
            clearInterval(urlChecker);
            observer.disconnect();
            console.log('⏰ 监听已停止');
        }, 10 * 60 * 1000);
    },

    // 4. 检查是否已经获取到缓存数据
    checkForCachedData: function() {
        console.log('🔍 检查缓存数据...');
        
        const sources = [
            'ultimate_anti_content',
            'temu_cached_anti_content',
            'optimized_anti_content'
        ];
        
        for (const source of sources) {
            const cached = localStorage.getItem(source);
            if (cached) {
                console.log(`🎉 找到缓存数据 ${source}:`, cached.substring(0, 50) + '...');
                return true;
            }
        }
        
        console.log('❌ 还没有找到缓存数据');
        return false;
    },

    // 5. 尝试导航到不同页面
    navigateToPages: function() {
        console.log('🧭 尝试导航到不同页面...');
        
        const pages = [
            '/main/product/seller-select',
            '/main/order/list',
            '/main/product/list',
            '/main/dashboard'
        ];
        
        let currentIndex = 0;
        
        const navigateNext = () => {
            if (currentIndex < pages.length) {
                const page = pages[currentIndex];
                console.log(`🧭 导航到: ${page}`);
                
                // 尝试通过修改URL导航
                if (window.location.pathname !== page) {
                    window.history.pushState({}, '', page);
                    
                    // 触发popstate事件
                    window.dispatchEvent(new PopStateEvent('popstate'));
                }
                
                currentIndex++;
                
                // 等待3秒再导航到下一个页面
                setTimeout(() => {
                    this.checkForCachedData();
                    setTimeout(navigateNext, 3000);
                }, 3000);
            } else {
                console.log('✅ 页面导航完成');
            }
        };
        
        navigateNext();
    },

    // 6. 运行完整的触发流程
    runFullTrigger: async function() {
        console.log('🚀 开始完整的API触发流程...');
        console.log('='.repeat(60));
        
        // 1. 检查初始状态
        console.log('1️⃣ 检查初始缓存状态');
        this.checkForCachedData();
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 2. 模拟用户操作
        console.log('2️⃣ 模拟用户操作');
        this.simulateUserActions();
        
        // 等待2秒
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 3. 直接调用API
        console.log('3️⃣ 直接调用API');
        await this.triggerApiDirectly();
        
        console.log('\n' + '-'.repeat(40) + '\n');
        
        // 4. 检查是否获取到数据
        console.log('4️⃣ 检查缓存状态');
        const hasData = this.checkForCachedData();
        
        if (!hasData) {
            console.log('\n' + '-'.repeat(40) + '\n');
            
            // 5. 开始监听和导航
            console.log('5️⃣ 开始监听和页面导航');
            this.waitForRealRequests();
            
            // 等待5秒再开始导航
            setTimeout(() => {
                this.navigateToPages();
            }, 5000);
        } else {
            console.log('🎉 已经获取到数据，无需继续触发');
        }
        
        console.log('\n' + '='.repeat(60));
        console.log('✅ API触发流程已启动');
        console.log('💡 请等待几分钟，然后检查localStorage中是否有anti-content数据');
    }
};

// 暴露到全局
window.apiTrigger = apiTrigger;

console.log('🔧 API触发工具已准备就绪！');
console.log('💡 运行 apiTrigger.runFullTrigger() 开始触发API请求');
console.log('💡 运行 apiTrigger.checkForCachedData() 检查缓存状态');

// 自动开始
apiTrigger.runFullTrigger();
